// Global değişkenler
let notificationManager;
let isDeviceConnected = false; // <PERSON>ihaz bağlantı durumu
let isWirelessConnected = false; // Kablosuz bağlantı durumu
let currentOS = 'AOS'; // Varsayılan olarak Android
let disconnectionNotificationShown = false; // Bağlantı kesildi bildirimi gösterildi mi
let connectionNotificationShown = false; // Bağlantı kuruldu bildirimi gösterildi mi
let userName = ''; // Kullanıcı adı
let userId = 0;
let userEmail = ''; // Kullanıcı e-posta adresi
let userRole = ''; // Kullanıcı rolü
let userFullName = ''; // Kullanıcı tam adı
let userCreatedAt = ''; // Kullanıcı hesap oluşturma tarihi
let userLastLogin = ''; // Kullanıcı son giriş tarihi
let isDarkMode = false; // Koyu tema durumu
let soundNotificationsEnabled = true; // <PERSON><PERSON><PERSON> bildirimler durumu
let currentFolderLocation = "Internal Storage"
let autoRefundTimer = null; // Otomatik refund için timer
let isAutoRefundActive = false; // Otomatik refund aktif mi
let isRefundInProgress = false; // Refund işlemi devam ediyor mu
let wasAutoRefundActiveBeforeRefund = false; // Refund başlamadan önce otomatik refund aktif miydi
// File Hasher için seçim ve bağlam menüsü değişkenleri
let fileHashSelectedRows = new Set();
let currentFileHashItem = null;
// Tarih formatını düzenle
const formatDate = (dateStr) => {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    // Timezone farkını hesaba katarak doğru tarihi elde et
    // getTimezoneOffset() negatif değer döndürür, bu yüzden çıkarmak yerine ekliyoruz
    const localDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
    return localDate.toLocaleDateString('tr-TR');
};
// HTML kodlarını decode eden fonksiyon
function decodeHTMLEntities(text) {
    if (!text) return '';

    // Geçici bir div elementi oluştur
    const textarea = document.createElement('textarea');
    // HTML içeriğini text olarak ata
    textarea.innerHTML = text;
    // Decoded değeri al
    return textarea.value;
}
// Context menüsünü konumlandıran fonksiyon - geliştirilmiş versiyon
function positionContextMenu2(menu, x, y) {
    if (!menu) return;

    // Menü boyutlarını al
    const menuWidth = menu.offsetWidth || 200;  // Varsayılan genişlik
    const menuHeight = menu.offsetHeight || 200; // Varsayılan yükseklik

    // Ekran boyutlarını al
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Scroll pozisyonlarını al
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Viewport sınırlarını hesapla
    const viewportLeft = scrollLeft;
    const viewportRight = scrollLeft + windowWidth;
    const viewportTop = scrollTop;
    const viewportBottom = scrollTop + windowHeight;

    let finalX = x;
    let finalY = y;

    // Yatay pozisyon kontrolü (sağa/sola)
    if (x + menuWidth > viewportRight) {
        // Sağa taşıyor, sola aç
        finalX = x - menuWidth;

        // Eğer sola açınca da taşıyorsa, viewport içinde en uygun yere yerleştir
        if (finalX < viewportLeft) {
            finalX = Math.max(viewportLeft + 5, viewportRight - menuWidth - 5);
        }
    }

    // Dikey pozisyon kontrolü (yukarı/aşağı)
    if (y + menuHeight > viewportBottom) {
        // Aşağıya taşıyor, yukarı aç
        finalY = y - menuHeight;

        // Eğer yukarı açınca da taşıyorsa, viewport içinde en uygun yere yerleştir
        if (finalY < viewportTop) {
            finalY = Math.max(viewportTop + 5, viewportBottom - menuHeight - 5);
        }
    }

    // Güvenlik kontrolü - menü hala viewport dışındaysa zorla içeri al
    finalX = Math.max(viewportLeft + 5, Math.min(finalX, viewportRight - menuWidth - 5));
    finalY = Math.max(viewportTop + 5, Math.min(finalY, viewportBottom - menuHeight - 5));

    // Pozisyonu ayarla
    menu.style.left = `${finalX}px`;
    menu.style.top = `${finalY}px`;
};
// Global notification helper function - tip parametresi ekleyelim
function showNotification(message, type = 'info') {
    if (notificationManager) {
        notificationManager.show(message, type);
    }
}
// Oyun seçimi kontrolü ve modal gösterimi
function checkGameSelected() {
    const gameSelect = document.getElementById('gameSelect');
    if (!gameSelect || gameSelect.value.includes('Seçilmedi')) {
        // Modalı göster
        const modal = document.getElementById('game-select-modal');
        modal.classList.remove('hidden');
        return false;
    }
    return true;
}
class NotificationManager {
    constructor() {
        this.notificationCount = 0;
        this.container = this.createContainer();
        this.audio = this.createAudio();
        this.maxNotifications = 3; // Maksimum bildirim sayısı
    }

    createContainer() {
        const container = document.createElement('div');
        container.id = 'notifications-container';
        // Pozisyonu sağ alt köşeye taşıyalım
        container.className = 'fixed right-4 bottom-11 z-50 space-y-2';
        document.body.appendChild(container);
        return container;
    }

    createAudio() {
        const audio = new Audio();
        // Base64 formatında kısa bir bildirim sesi
        audio.src = 'arriving.mp3';
        audio.volume = 0.5; // Ses seviyesi (0.0 - 1.0)
        return audio;
    }

    show(message, type = 'info') {
        // Maksimum bildirim sayısını kontrol et
        if (this.notificationCount >= this.maxNotifications) {
            // En eski bildirimi bul ve kaldır
            const notifications = this.container.querySelectorAll('.notification');
            if (notifications.length > 0) {
                this.close(notifications[0].id);
            }
        }

        // Ses çal
        this.audio.play().catch(e => console.log('Notification sound could not be played:', e));

        const notificationId = `notification-${Date.now()}`;
        const notification = this.createNotification(message, type, notificationId);

        this.container.appendChild(notification);
        this.notificationCount++;
        this.updatePositions();

        // Animasyon ekleyelim
        setTimeout(() => {
            notification.classList.add('translate-x-0', 'opacity-100');
        }, 50);

        setTimeout(() => this.close(notificationId), 3000);
    }

    close(notificationId) {
        const notification = document.getElementById(notificationId);
        if (!notification) return;

        // Kapanış animasyonu
        notification.classList.remove('translate-x-0', 'opacity-100');
        notification.classList.add('translate-x-full', 'opacity-0');

        setTimeout(() => {
            notification.remove();
            this.notificationCount--;
            this.updatePositions();
        }, 300);
    }

    updatePositions() {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach((notification, index) => {
            // Bildirimleri yukarıdan aşağıya doğru istifle
            // Her bildirim için 4rem (64px) + 0.5rem (8px) boşluk bırak
            notification.style.transform = `translateY(${index * 1}rem)`;
        });
    }

    createNotification(message, type, id) {
        const notification = document.createElement('div');
        notification.id = id;

        // Temel stiller ve animasyon sınıfları
        const baseClasses = [
            'notification',
            'p-4',
            'rounded-lg',
            'shadow-lg',
            'transform',
            'transition-all',
            'duration-300',
            'translate-x-full', // Başlangıçta sağdan dışarıda
            'opacity-0',        // Başlangıçta görünmez
            'flex',
            'items-center',
            'gap-2',
            'min-w-[300px]',
            'max-w-[400px]'
        ];

        // Tip'e göre arka plan ve simge belirleme
        const typeStyles = {
            success: {
                bg: 'bg-green-600',
                icon: '✓'
            },
            error: {
                bg: 'bg-red-600',
                icon: '✕'
            },
            warning: {
                bg: 'bg-yellow-500',
                icon: '⚠'
            },
            info: {
                bg: 'bg-blue-600',
                icon: 'ℹ'
            }
        };

        const style = typeStyles[type] || typeStyles.info;
        notification.className = [...baseClasses, style.bg, 'text-white'].join(' ');

        // İkon ve mesaj içeriği
        notification.innerHTML = `
            <span class="text-xl mr-2">${style.icon}</span>
            <p class="flex-1">${message}</p>
            <button onclick="notificationManager.close('${id}')" class="text-white hover:text-gray-200 transition-colors">
                ✕
            </button>
        `;

        return notification;
    }
}
function postMessage(message) {
    window.chrome.webview.postMessage(message);
    
}
function togglePassword() {
    const passwordInput = document.getElementById('loginPw');
    const icon = document.querySelector('.fa-eye, .fa-eye-slash');

    if (passwordInput.type === 'password') {
      // Şifreyi göster
      passwordInput.type = 'text';
      icon.classList.remove('fa-eye');
      icon.classList.add('fa-eye-slash');
      icon.setAttribute('title', 'Şifreyi gizle');
      // Animasyon efektini kaldırdık - GPU performansı için
    } else {
      // Şifreyi gizle
      passwordInput.type = 'password';
      icon.classList.remove('fa-eye-slash');
      icon.classList.add('fa-eye');
      icon.setAttribute('title', 'Şifreyi göster');
    }
}
function openTab(tabId) {
  const previousTab = document.querySelector('.tab-button.tab-active')?.getAttribute('data-tab');  
  // Profil sayfasını açmaya çalışırken kullanıcı giriş yapmamışsa uyarı göster ve işlemi durdur
  if (tabId === 'profile' && (!userName || userName.trim() === '')) {
    showNotification('Profil sayfasını görüntülemek için giriş yapmalısınız.', 'error');
    return;
  }  
  // Tab içeriklerini güncelle
  document.querySelectorAll('.tab-content').forEach(tab => {
    tab.classList.add('hidden');
  });
  document.getElementById(tabId).classList.remove('hidden');
  // Tab butonlarını güncelle
  document.querySelectorAll('.tab-button').forEach(btn => {
    btn.classList.remove('tab-active');
    btn.classList.add('tab-inactive');
  });
  // Aktif tab'ı işaretle
  const activeBtn = document.querySelector(`[data-tab="${tabId}"]`);
  if (activeBtn) {
    activeBtn.classList.remove('tab-inactive');
    activeBtn.classList.add('tab-active');
  }  
  // Profil sayfası açıldığında profil bilgilerini yükle
  if (tabId === 'profile') {
    loadProfileData();
  }  
  // Payments tab'ından başka bir tab'a geçildiğinde otomatik refund'u durdur
  if (previousTab === "payments" && tabId !== "payments") {
    if (isAutoRefundActive) {
      document.getElementById('autoRefund').checked = false;
      stopAutoRefund();
    }
  }
  // Video-SS tab'ı için özel işlem
  if (tabId === "video-ss") {
    postMessage({
      type: "tabChanged",
      tab: "video-ss"
    });
  }
  
  // TestRail tab'ı için özel işlem
  if (tabId === "testrail") {
    // Initialize TestRail if not already initialized
    if (typeof testRailConnectionStatus !== 'undefined' && !testRailConnectionStatus) {
      initTestRail();
    }
  }
  
  // Task Oluşturucu tab'ı için özel işlem
  else if (tabId === "studioqa") {
    // Sayfa yüklendiğinde atanabilecek kişileri ve sprint listesini getir
    postMessage({
      type: 'getAssignees'
    });
    postMessage({
      type: 'getSprints'
    });
    // Studio QA Tasks verilerini getir (ProjectID 39)
    loadStudioQATasks();
  }
  // QA Task sekmesi için özel işlem
  else if (tabId === "qa-task") {
    // Sayfa yüklendiğinde QA Task verilerini getir
    loadQaTasks();
  }
  // Google Ödemeleri sekmesi için özel işlem
  if (tabId === "payments") {
    // Sayfa yüklendiğinde ödemeleri getir
    refreshPayments(); 
    // Otomatik Refund checkbox'ını adminse göre görünür/gizli yap
    const autoRefundCheckboxContainer = document.getElementById('autoRefund').parentElement;
    if (!adminse()) {
        autoRefundCheckboxContainer.style.display = 'none';
    } else {
        autoRefundCheckboxContainer.style.display = 'flex';
    }
  }
}

// Profil bilgilerini yükle
function loadProfileData() {
  // Backend'den profil verilerini iste
  postMessage({
    type: 'getProfileData',
    username: userName
  });

  // Mevcut bilgileri göster (backend'den veri gelene kadar)
  updateProfileDisplay();
}

// Profil verilerini güncelle
function updateProfileDisplay(profileData = null) {
  // Kullanıcı bilgilerini göster
  const profileUsername = document.getElementById('profileUsername');
  const profileEmail = document.getElementById('profileEmail');
  const profileRole = document.getElementById('profileRole');
  const profileLastLogin = document.getElementById('profileLastLogin');
  const profileIpAddress = document.getElementById('profileIpAddress');

  if (profileData) {
    // Backend'den gelen veriler
    if (profileUsername) profileUsername.textContent = profileData.user || userName || '-';
    if (profileEmail) profileEmail.textContent = profileData.email || userEmail || '-';
    if (profileRole) profileRole.textContent = profileData.role || '-';
    if (profileLastLogin) {
      if (profileData.lastlogin && profileData.lastlogin !== '') {
        try {
          const date = new Date(profileData.lastlogin);
          if (!isNaN(date.getTime())) {
            profileLastLogin.textContent = formatDate(date);
          } else {
            profileLastLogin.textContent = profileData.lastlogin;
          }
        } catch (e) {
          profileLastLogin.textContent = profileData.lastlogin || '-';
        }
      } else {
        profileLastLogin.textContent = '-';
      }
    }
    if (profileIpAddress) profileIpAddress.textContent = profileData.ip || '-';
  } else {
    // Varsayılan değerler
    if (profileUsername) profileUsername.textContent = userName || '-';
    if (profileEmail) profileEmail.textContent = userEmail || '-';
    if (profileRole) profileRole.textContent = userRole || '-';
    if (profileLastLogin) {
      if (userLastLogin && userLastLogin !== '') {
        try {
          const date = new Date(userLastLogin);
          if (!isNaN(date.getTime())) {
            profileLastLogin.textContent = formatDate(date);
          } else {
            profileLastLogin.textContent = userLastLogin;
          }
        } catch (e) {
          profileLastLogin.textContent = userLastLogin || '-';
        }
      } else {
        profileLastLogin.textContent = '-';
      }
    }
    if (profileIpAddress) profileIpAddress.textContent = '-';
  }

  // Sistem bilgilerini göster
  const profileAppVersion = document.getElementById('profileAppVersion');
  const profileDeviceStatus = document.getElementById('profileDeviceStatus');
  const profileDeviceOS = document.getElementById('profileDeviceOS');
  const profileActiveGame = document.getElementById('profileActiveGame');
  const versionInfo = document.getElementById('versionInfo');

  if (profileAppVersion) profileAppVersion.textContent = versionInfo ? versionInfo.textContent || 'v1.0.0' : 'v1.0.0';
  if (profileDeviceStatus) profileDeviceStatus.textContent = isDeviceConnected ? 'Bağlı' : 'Bağlı Değil';
  if (profileDeviceOS) profileDeviceOS.textContent = currentOS === 'AOS' ? 'Android' : 'iOS';
  if (profileActiveGame) {
    const gameSelect = document.getElementById('gameSelect');
    const selectedGame = gameSelect ? gameSelect.options[gameSelect.selectedIndex]?.text : null;
    profileActiveGame.textContent = selectedGame || 'Seçili Değil';
  }

  // Tema ve bildirim ayarlarını göster
  const darkModeToggle = document.getElementById('darkModeToggle');
  const soundNotificationsToggle = document.getElementById('soundNotificationsToggle');
  
  if (darkModeToggle) darkModeToggle.checked = isDarkMode;
  if (soundNotificationsToggle) soundNotificationsToggle.checked = soundNotificationsEnabled;
}



// Profil verilerini yenile
function refreshProfileData() {
  showNotification('Profil verileri yenileniyor...', 'info');
  loadProfileData();
}
function switchAdminTab(tabName) {
  // Admin tab içeriklerini gizle
  document.querySelectorAll('.admin-tab-content').forEach(tab => {
    tab.classList.add('hidden');
  });
  // Seçilen tab'ı göster
  document.getElementById('admin-' + tabName).classList.remove('hidden');
  // Tab butonlarını güncelle
  document.querySelectorAll('.admin-tab').forEach(btn => {
    btn.classList.remove('text-custom', 'border-b-2', 'border-custom');
  });
  // Aktif tab'ı işaretle
  const activeTab = document.querySelector(`[data-admin-tab="${tabName}"]`);
  activeTab.classList.add('text-custom', 'border-b-2', 'border-custom');

  // File Hasher tab'ı için özel işlem
  if (tabName === 'file-hasher') {
    // Dosya hash verilerini getir
    getFileHashes();
  }
  // Oyun verileri tab'ı için özel işlem
  else if (tabName === 'game-data') {
    // Oyun verilerini getir
    if (gamesData.length === 0) {
      getGamesData();
    }
  }
  // Sürüm tab'ı için özel işlem
  else if (tabName === 'version') {
    // Sürüm verilerini getir
    getVersionData();
  }
  // Studio QA tab'ı için özel işlem
  else if (tabName === 'studio-qa') {
    // Studio QA verilerini getir
    getStudioQAData();
  }
  // QA Task tab'ı için özel işlem
  else if (tabName === 'qa-task') {
    // QA Task verilerini getir
    loadQaTasks();
  }
  // All Tasks tab'ı için özel işlem
  else if (tabName === 'all-tasks') {
    // All Tasks verilerini getir
    loadAllTasks();
  }
  // Data Viewer tab'ı için özel işlem
  else if (tabName === 'dataviewer') {
    // FTP'den log dosyalarını getir
    getDataViewerFiles();
  }
}
function receiveFromCSharp(message) {
  document.getElementById("response").innerText = "C#'tan gelen mesaj: " + message;
}
// Event listener'ları ayarlayan fonksiyon
function setupEventListeners() {
  const alwaysTopCheckbox = document.getElementById('always-top');
  const autoBundleCheckbox = document.getElementById('auto-bundle');
  const videoRecordLoopCheckbox = document.getElementById('videorecordloop');
  const gameSelect = document.getElementById('gameSelect');

  if (alwaysTopCheckbox) {
    alwaysTopCheckbox.addEventListener('change', (e) => {
      postMessage({
        type: 'setAlwaysTop',
        value: e.target.checked
      });
    });
  }

  if (autoBundleCheckbox) {
    autoBundleCheckbox.addEventListener('change', (e) => {
      postMessage({
        type: 'setAutoBundle',
        value: e.target.checked
      });
    });
  }

  if (videoRecordLoopCheckbox) {
    videoRecordLoopCheckbox.addEventListener('change', (e) => {
      const isContinuousRecording = e.target.checked;

      postMessage({
        type: 'setContinuousRecording',
        value: isContinuousRecording
      });

      // If continuous recording is disabled and we're not currently recording,
      // stop the timer but don't reset it
      const recordButton = document.querySelector('button[onclick="startVideoRecording()"]');
      const isRecording = recordButton && recordButton.textContent === 'Recording...';

      if (!isContinuousRecording && !isRecording) {
        videoTimerStop();
      }
    });
  }

  if (gameSelect) {
    // Prevent multiple event handlers from being attached
    gameSelect.removeEventListener('change', handleGameSelectChange);
    gameSelect.addEventListener('change', handleGameSelectChange);
  }
}
// Separate function to handle game selection change
function handleGameSelectChange(e) {
  

  // Debug: Log the call stack to see where this is being called from
  

  // Only send one message to backend
  postMessage({
    type: 'gameSelected',
    value: e.target.value
  });

  // Update selectedGameName element
  const selectedGameNameElement = document.getElementById('selectedGameName');
  if (selectedGameNameElement) {
    selectedGameNameElement.textContent = e.target.value;
  }

  // Check if a valid game is selected
  if (!checkGameSelected()) return;

  // Show loading spinners
  const spinner = `<div class="spinner-border animate-spin inline-block w-4 h-4 border-2 rounded-full border-t-transparent"></div>`;
  document.getElementById('app-version').innerHTML = spinner;
  document.getElementById('build-number').innerHTML = spinner;
  document.getElementById('bundle-id').innerHTML = spinner;
  document.getElementById('target-api').innerHTML = spinner;
}
// Function to get available storage locations
function getStorageLocations() {
    // Send message to C# to get available storage locations
    window.chrome.webview.postMessage({
        type: "getStorageLocations"
    });
}
// TestRail message wrapper function
function sendTestRailMessage(action, data = {}) {
    const message = {
        type: 'testrail',
        action: action,
        ...data
    };
    window.chrome.webview.postMessage(message);
}
// Function to populate storage select with options
function populateStorageSelect(locations) {
    // Clear existing options
    storageSelect.innerHTML = '';
    if (locations.length === 0) {
        return;
    }

    // Add options for each storage location
    locations.forEach(location => {
        const option = document.createElement('option');
        option.value = location.path;
        option.textContent = `${location.name} (${location.size})`;
        storageSelect.appendChild(option);
    });

    // Set the currently selected location
    if (currentFolderLocation) {
        for (let i = 0; i < storageSelect.options.length; i++) {
            if (storageSelect.options[i].value === currentFolderLocation) {
                storageSelect.selectedIndex = i;
                break;
            }
        }
    }
}
// Dosya adı linkine tıklandığında çalışacak örnek fonksiyon
function handleFileNameClick(fileNames) {
  // Örneğin, C# tarafına dosya açma isteği gönderebilirsiniz:
  postMessage({ type: "openFile", filename: fileNames });
}
function handleDragOver(event) {
  event.preventDefault();
  event.dataTransfer.dropEffect = 'copy';
}
let isAdmin = false; // Global değişken olarak tanımlayalım
//let userRole = ''; // Kullanıcı rolü için global değişken
// Tek bir openInternalLink fonksiyonu tanımlandı.
async function openInternalLink() {
  
  try {
    if (!checkGameSelected()) return;

    const gameSelect = document.getElementById('gameSelect');
    const osSelect = document.querySelector('select:not(#gameSelect)');

    await chrome.webview.postMessage({
      type: 'openInternalLink',
      data: {
        game: gameSelect.value,
        os: osSelect.value
      }
    });
    
  } catch (error) {
    console.error('openInternalLink error:', error);
  }
}
async function clearAppData() {
  
  try {
    if (!checkGameSelected()) return;

    const gameSelect = document.getElementById('gameSelect');
    const osSelect = document.querySelector('select:not(#gameSelect)');

    showConfirmationModal('Uygulama verilerini silmek istediğinizden emin misiniz?', async () => {
      await chrome.webview.postMessage({
        type: 'clearAppData',
        data: {
          game: gameSelect.value,
          os: osSelect.value
        }
      });
      
    });
  } catch (error) {
    console.error('clearAppData error:', error);
  }
}
async function startVideoRecording() {
  try {
    const recordButton = document.querySelector('button[onclick="startVideoRecording()"]');
    recordButton.textContent = 'Recording...';

    // Get the state of the continuous recording checkbox
    const continuousRecording = document.getElementById('videorecordloop').checked;

    // Start the video timer
    videoTimerStart();

    await chrome.webview.postMessage({
      type: 'startVideoRecording',
      continuousRecording: continuousRecording
    });
  } catch (error) {
    console.error('Video recording error:', error);
  }
}
function takeScreenshot() {
  postMessage({ type: 'takeScreenshot' });
}
function restartDevice() {
  showConfirmationModal('Cihazı yeniden başlatmak istediğinizden emin misiniz?', () => {
    postMessage({ type: 'restartDevice' });
  });
}
// Oyun listesini yenileme fonksiyonu
function refreshGameList() {
  postMessage({ type: 'refreshGameList' });
}
function refreshAppInfo() {
  if (!checkGameSelected()) return;
  const spinner = `<div class="spinner-border animate-spin inline-block w-4 h-4 border-2 rounded-full border-t-transparent"></div>`;
  document.getElementById('app-version').innerHTML = spinner;
  document.getElementById('build-number').innerHTML = spinner;
  document.getElementById('bundle-id').innerHTML = spinner;
  document.getElementById('target-api').innerHTML = spinner;
  postMessage({ type: 'refreshAppInfo' });
}
// Global timer variables for log timer
let logTimerInterval;
let logTimerSeconds = 0;

// Global timer variables for video timer
let videoTimerInterval;
let videoTimerSeconds = 0;
let videoTimerPaused = false;
let videoTimerRunning = false;
function timerStart() {
  // Reset timer variables
  clearInterval(logTimerInterval);
  logTimerSeconds = 0;

  // Update timer display immediately
  updateLogTimer();

  // Start the timer interval
  logTimerInterval = setInterval(updateLogTimer, 1000);
}
function updateLogTimer() {
  const logTimerElement = document.getElementById('logTimer');
  if (!logTimerElement) return;

  const minutes = String(Math.floor(logTimerSeconds / 60)).padStart(2, '0');
  const secs = String(logTimerSeconds % 60).padStart(2, '0');
  logTimerElement.textContent = `${minutes}:${secs}`;
  logTimerSeconds++;
}
function timerStop() {
  clearInterval(logTimerInterval);
  logTimerInterval = null;
}
// Video timer functions
function videoTimerStart() {
  // If timer is already running, don't restart it
  if (videoTimerInterval) return;

  // If timer was paused, resume it
  if (videoTimerPaused) {
    videoTimerPaused = false;
    updatePauseButtonIcon();
  }

  // Update timer display immediately
  updateVideoTimer();

  // Start the timer interval
  videoTimerInterval = setInterval(updateVideoTimer, 1000);
  videoTimerRunning = true;
  videoTimerPaused = false;

  // Make sure the pause button shows the pause icon
  updatePauseButtonIcon();
}
function updateVideoTimer() {
  const videoTimerElement = document.getElementById('videoTimer');
  if (!videoTimerElement) return;

  const minutes = String(Math.floor(videoTimerSeconds / 60)).padStart(2, '0');
  const secs = String(videoTimerSeconds % 60).padStart(2, '0');
  videoTimerElement.textContent = `${minutes}:${secs}`;
  videoTimerSeconds++;
}
function videoTimerStop() {
  clearInterval(videoTimerInterval);
  videoTimerInterval = null;
  videoTimerRunning = false;

  // Set timer to paused state when stopped
  videoTimerPaused = true;

  // Update the pause button icon to show play icon
  updatePauseButtonIcon();

  // Don't reset the timer automatically anymore
  // Reset should only happen when the reset button is clicked
}
function videoTimerPause() {
  if (!videoTimerRunning) {
    // If timer is not running, start it
    videoTimerStart();
    videoTimerRunning = true;
    videoTimerPaused = false;
    updatePauseButtonIcon();
    return;
  }

  if (videoTimerPaused) {
    // Resume timer
    videoTimerPaused = false;
    videoTimerInterval = setInterval(updateVideoTimer, 1000);
  } else {
    // Pause timer
    videoTimerPaused = true;
    clearInterval(videoTimerInterval);
    videoTimerInterval = null;
  }

  // Update pause button icon
  updatePauseButtonIcon();
}
function updatePauseButtonIcon() {
  const pauseButton = document.getElementById('videoPauseBtn');
  if (!pauseButton) return;

  const icon = pauseButton.querySelector('i');
  if (!icon) return;

  if (videoTimerPaused) {
    icon.className = 'fas fa-play text-xs';
    pauseButton.title = 'Devam Et';
  } else {
    icon.className = 'fas fa-pause text-xs';
    pauseButton.title = 'Duraklat';
  }
}
function videoTimerReset() {
  videoTimerSeconds = 0;
  const videoTimerElement = document.getElementById('videoTimer');
  if (videoTimerElement) {
    videoTimerElement.textContent = '00:00';
  }
}
function startLogRecording() {
  postMessage({ type: 'startLogRecording', lognote: logNote.value });
  timerStart();
}
function recordLast30Seconds() {
  const button = document.getElementById('nvidiaRecordButton');
  button.textContent = 'Kayıt Alındı';
  button.classList.add('animate-pulse');
  setTimeout(() => {
    button.classList.remove('animate-pulse');
    button.textContent = 'Record Last 30 Seconds';
  }, 2000);
  postMessage({ type: 'recordLast30Seconds' });
}
// Genel onay modal fonksiyonu
function showConfirmationModal(message, confirmCallback) {
    const modal = document.getElementById('confirmationModal');
    const modalMessage = document.getElementById('confirmationMessage');
    const confirmButton = document.getElementById('confirmButton');
    const cancelButton = document.getElementById('cancelButton');

    // Mesajı ayarla
    modalMessage.textContent = message;

    // Modal'ı göster
    modal.classList.remove('hidden');

    // Önceki event listener'ları temizle
    const newConfirmButton = confirmButton.cloneNode(true);
    const newCancelButton = cancelButton.cloneNode(true);
    confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);
    cancelButton.parentNode.replaceChild(newCancelButton, cancelButton);

    // Yeni event listener'ları ekle
    newConfirmButton.addEventListener('click', () => {
        modal.classList.add('hidden');
        if (confirmCallback) confirmCallback();
    });

    newCancelButton.addEventListener('click', () => {
        modal.classList.add('hidden');
    });

    // Modal dışına tıklandığında kapat
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.add('hidden');
        }
    });
}
// Uygulamayı kapatma fonksiyonu
async function closeApplication() {
    showConfirmationModal(
        'Uygulamayı kapatmak istediğinizden emin misiniz?',
        async () => {
            await chrome.webview.postMessage({
                type: 'closeUtility'
            });
        }
    );
}
async function launchApp() {
  
  try {
    if (!checkGameSelected()) return;

    const gameSelect = document.getElementById('gameSelect');
    const osSelect = document.querySelector('select:not(#gameSelect)');

    await chrome.webview.postMessage({
      type: 'launchApp',
      data: {
        game: gameSelect.value,
        os: osSelect.value
      }
    });
    
  } catch (error) {
    console.error('launchApp error:', error);
  }
}
async function closeApp() {
  
  try {
    if (!checkGameSelected()) return;

    const gameSelect = document.getElementById('gameSelect');
    const osSelect = document.querySelector('select:not(#gameSelect)');

    await chrome.webview.postMessage({
      type: 'closeApp',
      data: {
        game: gameSelect.value,
        os: osSelect.value
      }
    });
    
  } catch (error) {
    console.error('closeApp error:', error);
  }
}
async function viewInStore() {
  
  try {
    if (!checkGameSelected()) return;

    const gameSelect = document.getElementById('gameSelect');
    const osSelect = document.querySelector('select:not(#gameSelect)');

    await chrome.webview.postMessage({
      type: 'viewInStore',
      data: {
        game: gameSelect.value,
        os: osSelect.value
      }
    });
    
  } catch (error) {
    console.error('viewInStore error:', error);
  }
}
async function uninstallApp() {
  
  try {
    const gameSelect = document.getElementById('gameSelect');
    const osSelect = document.querySelector('select:not(#gameSelect)');

    if (!checkGameSelected()) return;

    showConfirmationModal('Uygulamayı kaldırmak istediğinizden emin misiniz?', async () => {
      await chrome.webview.postMessage({
        type: 'uninstallApp',
        data: {
          game: gameSelect.value,
          os: osSelect.value
        }
      });
      
    });
  } catch (error) {
    console.error('uninstallApp error:', error);
  }
}
async function sendText() {
  let text = document.getElementById("sendTextArea").value;
  await chrome.webview.postMessage({
    type: 'sendText',
    text: text,
  });
}
/* Admin Komutları için stub fonksiyonlar */
function turnOffWifi() {
  postMessage({ type: 'turnOffWifi' });
}
function switchToWireless() {
  if (isWirelessConnected) {
    // Kablosuz bağlantı aktif ise, kapat
    postMessage({ type: 'disconnectWireless' });
  } else {
    // Kablosuz bağlantı değil ise, bağlan
    postMessage({ type: 'switchToWireless' });
  }
}
function runMonkey() {
  postMessage({ type: 'runMonkey' });
}
function uninstallAllApps() {
  showConfirmationModal('Tüm uygulamaları kaldırmak istediğinizden emin misiniz?', () => {
    postMessage({ type: 'uninstallAllApps' });
  });
}
function killAdbServer() {
  postMessage({ type: 'killAdbServer' });
}

// Cihaz bilgilerini kopyala
function copyDeviceInfo() {
  // Cihaz bilgilerini al
  const brand = document.getElementById('device-brand').textContent;
  const model = document.getElementById('device-model').textContent;
  const version = document.getElementById('device-version').textContent;
  const chipset = document.getElementById('device-chipset').textContent;
  const cpu = document.getElementById('device-cpu').textContent;
  const resolution = document.getElementById('device-resolution').textContent;
  const language = document.getElementById('device-language').textContent;

  // Bilgileri formatlı bir şekilde birleştir
  const deviceInfo = `Marka: ${brand}\nModel: ${model}\nSürüm: ${version}\nChipset: ${chipset}\nCPU: ${cpu}\nÇözünürlük: ${resolution}\nCihaz Dili: ${language}`;

  // Bilgileri C# tarafına gönder (panoya kopyalamak için)
  postMessage({
    type: 'copyDeviceInfo',
    data: deviceInfo
  });

  // Kullanıcıya bildirim göster
  showNotification('Cihaz bilgileri panoya kopyalandı.', 'success');
}

// Task Creator ile ilgili fonksiyonlar
function clearTaskForm() {
    document.getElementById('taskCreatorForm').reset();
}

// Studio QA Task oluşturma fonksiyonu
function createStudioQATask() {
    showTaskModal('studioQA');
}

// Live Test Task oluşturma fonksiyonu
function createLiveTestTask() {
    showTaskModal('liveTest');
}

// Modal işlemleri için gerekli fonksiyonlar
function showTaskModal(taskType) {
    // Modal başlığını ayarla
    const modalTitle = document.getElementById('modalTitle');
    if (taskType === 'studioQA') {
        modalTitle.textContent = 'Stüdyo QA Task Oluştur';
    } else if (taskType === 'liveTest') {
        modalTitle.textContent = 'Live Test Task Oluştur';
    }

    // Mevcut seçili oyun bilgilerini al
    const gameSelect = document.getElementById('gameSelect');
    const osSelect = document.querySelector('select:not(#gameSelect)');
    const appVersion = document.getElementById('app-version').textContent;
    const buildNumber = document.getElementById('build-number').textContent;

    // Modal alanlarını doldur
    document.getElementById('modalGameName').value = gameSelect.value;
    document.getElementById('modalOS').value = osSelect.value;
    document.getElementById('modalVersion').value = appVersion;
    document.getElementById('modalBuildNumber').value = buildNumber;

    // Modal'ı göster
    const modal = document.getElementById('taskModal');
    modal.classList.remove('hidden');

    // Modal verilerini sakla
    modal.dataset.taskType = taskType;
}

function closeTaskModal() {
    document.getElementById('taskModal').classList.add('hidden');
}

async function submitTaskModal() {
    const modal = document.getElementById('taskModal');
    const taskType = modal.dataset.taskType;

    const taskData = {
        type: taskType,
        game: document.getElementById('modalGameName').value,
        os: document.getElementById('modalOS').value,
        version: document.getElementById('modalVersion').value,
        buildNumber: document.getElementById('modalBuildNumber').value
    };

    // Boş alan kontrolü
    if (!taskData.game || !taskData.os || !taskData.version || !taskData.buildNumber) {
        showNotification('Lütfen tüm alanları doldurun', 'warning');
        return;
    }

    // Geçerli oyun kontrolü
    if (taskData.game.includes('Seçilmedi')) {
        showNotification('Lütfen geçerli bir oyun seçin!', 'warning');
        return;
    }

    try {
        await postMessage({
            type: 'createTaskWithDetails',
            data: taskData
        });

        closeTaskModal();
        showNotification('Task oluşturuluyor','info');
    } catch (error) {
        console.error('Task oluşturma hatası:', error);
        showNotification('Task oluşturulurken bir hata oluştu');
    }
}

// Modal dışına tıklandığında kapatma
document.addEventListener('click', function(event) {
    const modal = document.getElementById('taskModal');
    if (event.target === modal) {
        closeTaskModal();
    }
});

// Payments tablosu için gerekli değişkenler
let currentSort = { column: 'date', direction: 'desc' };
let paymentData = [];

// Global değişkenler
let isSelecting = false;
let startRowIndex = -1;
let lastSelectedIndex = -1;

// Tabloyu sıralama fonksiyonu
function sortPaymentsTable(column) {
    const table = document.getElementById('paymentsTable');
    const headers = table.querySelectorAll('th');
    const currentHeader = table.querySelector(`th[data-sort="${column}"]`);

    // Sıralama yönünü belirle
    if (currentSort.column === column) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.column = column;
        currentSort.direction = 'asc';
    }

    // Tüm sort ikonlarını resetle
    headers.forEach(header => {
        const icon = header.querySelector('i');
        icon.className = 'fas fa-sort text-gray-500 ml-1';
    });

    // Aktif sütunun ikonunu güncelle
    const icon = currentHeader.querySelector('i');
    icon.className = `fas fa-sort-${currentSort.direction === 'asc' ? 'up' : 'down'} text-custom ml-1`;

    // Verileri sırala ve tabloyu güncelle
    refreshPaymentsTable();
}

// Tabloyu güncelleme fonksiyonu
function refreshPaymentsTable() {
    const tbody = document.querySelector('#paymentsTable tbody');
    const searchText = document.getElementById('paymentSearch').value.toLowerCase();
    const showPending = document.getElementById('showPending').checked;

    if (!paymentData || paymentData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-4 py-3 text-center text-gray-400">
                    Veri bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    let filteredData = paymentData.filter(payment => {
        const matchesSearch = Object.values(payment).some(value =>
            String(value).toLowerCase().includes(searchText)
        );
        const matchesPending = !showPending || payment.status === 'Bekliyor' || payment.status === 'İşlem Başarısız';
        return matchesSearch && matchesPending;
    });    // Orijinal dizideki index'leri bulmak için map kullanıyoruz
    tbody.innerHTML = filteredData.map((payment) => {
        // Orijinal dizideki index'i bul
        const originalIndex = paymentData.findIndex(p => p.id === payment.id);
        return `
            <tr class="hover:bg-gray-600 transition-colors payment-row" 
                data-index="${originalIndex}" 
                data-payment-id="${payment.id}"
                data-payment-pin="${payment.paymentPin || ''}"
                data-bundle-id="${payment.bundleId || ''}"
                data-app-name="${payment.appName || ''}"
                data-tester="${payment.tester || ''}"
                data-status="${payment.status || 'Bekliyor'}">
            <td class="px-4 py-3">${payment.id || ''}</td>
            <td class="px-4 py-3">${payment.appName || ''}</td>
            <td class="px-4 py-3">${payment.tester || ''}</td>
            <td class="px-4 py-3">
                <span class="truncate-cell" title="${payment.bundleId || ''}">${payment.bundleId}</span>
            </td>
            <td class="px-4 py-3">
                <span class="truncate-cell" title="${payment.paymentPin || ''}">${payment.paymentPin}</span>
            </td>
            <td class="px-4 py-3">
                <span class="px-2 py-1 rounded-full text-sm ${
                    payment.status === 'Tamamlandı'
                        ? 'bg-green-500/20 text-green-500'
                        : 'bg-yellow-500/20 text-yellow-500'
                }">
                    ${payment.status || 'Bekliyor'}
                </span>
            </td>
        </tr>
        `;
    }).join('');

    if (filteredData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-4 py-3 text-center text-gray-400">
                    Arama kriterlerine uygun veri bulunamadı
                </td>
            </tr>
        `;
    }

    // Seçim işlemleri için event listener'ları ekle
    setupSelectionListeners();

    // Context menu listener'larını ekle
    setupContextMenu();
}

function setupSelectionListeners() {
    const tbody = document.querySelector('#paymentsTable tbody');

    tbody.addEventListener('mousedown', (e) => {
        // Sağ tık için kontrol ekleyelim
        if (e.button === 2) { // 2 = sağ tık
            return; // Sağ tıkta seçim işlemini atlayalım
        }

        if (!e.target.closest('tr')) return;

        isSelecting = true;
        const row = e.target.closest('tr');
        startRowIndex = parseInt(row.dataset.index);
        lastSelectedIndex = startRowIndex;

        // Ctrl tuşuna basılı değilse önceki seçimleri temizle
        if (!e.ctrlKey) {
            clearSelection();
        }

        toggleRowSelection(row);

        // Text seçimini engelle
        e.preventDefault();
    });

    // Mouse move eventi
    tbody.addEventListener('mousemove', (e) => {
        if (!isSelecting) return;

        const row = e.target.closest('tr');
        if (!row) return;

        const currentIndex = parseInt(row.dataset.index);
        if (currentIndex === lastSelectedIndex) return;

        // Seçim aralığını güncelle
        const rows = tbody.querySelectorAll('tr');
        const start = Math.min(startRowIndex, currentIndex);
        const end = Math.max(startRowIndex, currentIndex);

        rows.forEach((row) => {
            const rowIndex = parseInt(row.dataset.index);
            if (rowIndex >= start && rowIndex <= end) {
                selectRow(row);
            } else if (!e.ctrlKey) {
                deselectRow(row);
            }
        });

        lastSelectedIndex = currentIndex;
    });

    // Mouse up eventi
    document.addEventListener('mouseup', () => {
        isSelecting = false;
    });
}

function clearSelection() {
    const rows = document.querySelectorAll('#paymentsTable tbody tr');
    rows.forEach(row => deselectRow(row));
}

function toggleRowSelection(row) {
    if (row.classList.contains('selected')) {
        deselectRow(row);
    } else {
        selectRow(row);
    }
}

function selectRow(row) {
    row.classList.add('selected');
    row.classList.add('bg-blue-600/50');
}

function deselectRow(row) {
    row.classList.remove('selected');
    row.classList.remove('bg-blue-600/50');
}

// Seçili satırları getiren yardımcı fonksiyon
function getSelectedRows() {
    const selectedRows = Array.from(document.querySelectorAll('#paymentsTable tbody tr.selected'));
    return selectedRows.map(row => {
        const index = parseInt(row.dataset.index);
        return {
            element: row,
            data: paymentData[index],
            originalIndex: index
        };
    });
}

// Ödeme kaydetme fonksiyonu
function savePayments() {
    const details = document.getElementById('paymentDetails').value;
    const selectedGame = document.getElementById('selectedGameName').textContent;

    if(selectedGame === '-'){
        showNotification('Lütfen bir oyun seçin');
        return;
    }
    if (!details.trim()) {
        showNotification('Lütfen ödeme detaylarını girin');
        return;
    }

    // Backend'e gönderme işlemi
    postMessage({
        type: 'savePayment',
        data: {
            details,
            game: selectedGame
        }
    });

    // Text alanını temizle
    document.getElementById('paymentDetails').value = '';
}

// Ödemeleri yenileme fonksiyonu
function refreshPayments() {
    postMessage({
        type: 'getPayments'
    });
}

// Context menu için event listener'ları
let contextMenuInitialized = false;

function setupContextMenu() {
    if (contextMenuInitialized) return;

    const tbody = document.querySelector('#paymentsTable tbody');
    const contextMenu = document.getElementById('paymentsContextMenu');

    // Sağ tık eventi
    tbody.addEventListener('contextmenu', (e) => {
        e.preventDefault();

        const clickedRow = e.target.closest('tr');
        if (!clickedRow) return;

        // Eğer tıklanan satır seçili değilse ve Ctrl tuşuna basılı değilse
        if (!clickedRow.classList.contains('selected') && !e.ctrlKey) {
            clearSelection();
            selectRow(clickedRow);
        }

        // Seçili satır kontrolü
        const selectedRows = getSelectedRows();
        if (selectedRows.length === 0) return;

        // Context menu'yü konumlandır - ekranın dışına taşmasını önle
        positionContextMenu(contextMenu, e.clientX, e.clientY);
        contextMenu.classList.remove('hidden');
    });

    // Sayfa herhangi bir yerine tıklandığında context menu'yü gizle
    document.addEventListener('click', (e) => {
        if (!contextMenu.contains(e.target)) {
            contextMenu.classList.add('hidden');
        }
    });

    // Context menu işlemleri
    document.getElementById('paymentcontextDelete').addEventListener('click', () => {
        const selectedRows = getSelectedRows();
        // Filter rows where tester matches current userName
        let userPayments = selectedRows.filter(row => row.data.tester === userName);
        // If user is admin, allow them to delete any payment
        if (adminse()) {
            userPayments = selectedRows;
        }
        if (userPayments.length === 0) {
            showNotification('Sadece kendi ödeme kayıtlarınızı silebilirsiniz.', 'error');
            contextMenu.classList.add('hidden');
            return;
        }

        if (userPayments.length < selectedRows.length) {
            showNotification('Sadece kendi ödeme kayıtlarınız silinecek.', 'warning');
        }

        const selectedPayments = userPayments.map(row => ({
          id: row.data.id,
          paymentPin: row.data.paymentPin,
          bundleId: row.data.bundleId,
          appName: row.data.appName,
          tester: row.data.tester,
          status : row.data.status
        }));

        postMessage({
            type: 'deletePayments',
            data: { payments: selectedPayments }
        });

        contextMenu.classList.add('hidden');
    });

    document.getElementById('contextStatusPending').addEventListener('click', () => {
        if (!adminse()) {
            showNotification('Bu işlemi yapmaya yetkiniz yok!', 'error');
            return;
        }
        updatePaymentStatus('Bekliyor');
        contextMenu.classList.add('hidden');
    });

    document.getElementById('contextStatusCompleted').addEventListener('click', () => {
        if (!adminse()) {
            showNotification('Bu işlemi yapmaya yetkiniz yok!', 'error');
            return;
        }
        updatePaymentStatus('Tamamlandı');
        contextMenu.classList.add('hidden');
    });

    document.getElementById('contextRefund').addEventListener('click', () => {
        // admin değilse yapamasın.
        if (!adminse()) {
            showNotification('Bu işlemi yapmaya yetkiniz yok!', 'error');
            return;
        }
        const selectedRows = getSelectedRows();
        // Seçili satırları loading durumuna geçir
        selectedRows.forEach(row => {
            const statusCell = row.element.querySelector('td:last-child span');
            statusCell.className = 'px-2 py-1 rounded-full text-sm bg-blue-500/20 text-blue-500';
            statusCell.innerHTML = `
                <svg class="inline animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                İşleniyor...
            `;
        });

        const selectedPayments = selectedRows.map(row => ({
            id: row.data.id,
            paymentPin: row.data.paymentPin,
            bundleId: row.data.bundleId,
            appName: row.data.appName,
            tester: row.data.tester
        }));        // Refund işlemine başlamadan önce otomatik refund durumunu kaydet
        wasAutoRefundActiveBeforeRefund = isAutoRefundActive;
        
        // Eğer otomatik refund aktifse, duraklat
        if (isAutoRefundActive) {
            clearInterval(autoRefundTimer);
            autoRefundTimer = null;
            
        }
        
        // Refund işlemini başlat
        isRefundInProgress = true;
        postMessage({
            type: 'refundPayments',
            data: { payments: selectedPayments }
        });

        contextMenu.classList.add('hidden');
    });

    contextMenuInitialized = true;
}

// adminse fonksiyonu oluşturalım.
function adminse() {
    return isAdmin;
}

// QA Task ile ilgili değişkenler
let qaTasks = [];
let qaTasksSort = { column: 'id', direction: 'asc' };
let qaTaskStatuses = [];
let qaTaskPriorities = [];
let qaTaskUsers = [];
let currentQaTask = null;

// All Tasks ile ilgili değişkenler
let allTasks = [];
let allTasksSort = { column: 'id', direction: 'asc' };
let currentAllTask = null;

// Studio QA Tasks ile ilgili değişkenler
let studioQATasks = [];
let studioQATasksSort = { column: 'id', direction: 'asc' };
let currentStudioQATask = null;

// QA Task verilerini yükleyen fonksiyon
function loadQaTasks() {
    showLoadingIndicator('qaTaskTableBody');

    postMessage({
        type: 'getQaTasks'
    });
}

// QA Task verilerini tabloya ekleyen fonksiyon
function displayQaTasks(tasks) {
    qaTasks = tasks;
    const tableBody = document.getElementById('qaTaskTableBody');
    tableBody.innerHTML = '';

    if (qaTasks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4 text-gray-400">
                    Herhangi bir task bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Önce verileri sırala
    const sortedTasks = sortQaTasks(qaTasks, qaTasksSort.column, qaTasksSort.direction === 'asc');

    // Tabloya ekle
    sortedTasks.forEach((task, index) => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-700 transition-colors cursor-pointer';
        row.setAttribute('data-id', task.Id);
        row.setAttribute('data-index', index);

        // Çift tıklama olayı ekle
        row.addEventListener('dblclick', () => {
            openTaskInRedmine(task.Id);
        });

        // Durum rengini belirle
        let statusClass = '';
        if (task.Status && task.Status.Name) {
            const statusName = task.Status.Name.toLowerCase();
            if (statusName.includes('new') || statusName.includes('yeni')) {
                statusClass = 'bg-blue-800/30';
            } else if (statusName.includes('progress') || statusName.includes('devam')) {
                statusClass = 'bg-yellow-800/30';
            } else if (statusName.includes('resolved') || statusName.includes('çözüldü')) {
                statusClass = 'bg-green-800/30';
            } else if (statusName.includes('closed') || statusName.includes('kapandı')) {
                statusClass = 'bg-gray-800/30';
            }
        }

        if (statusClass) {
            row.classList.add(statusClass);
        }


        // Atanan kişi bilgisini düzenle
        let assignedToText = '';
        if (task.AssignedTo) {
            if (task.AssignedTo.Name) {
                assignedToText = task.AssignedTo.Name;
            } else {
                // Kullanıcı ID'sine göre qaTaskUsers içinden bul
                const assignedUser = qaTaskUsers.find(user => user.Id === task.AssignedTo.Id);
                if (assignedUser) {
                    assignedToText = assignedUser.FirstName && assignedUser.LastName ?
                        `${assignedUser.FirstName} ${assignedUser.LastName}` :
                        assignedUser.Login;
                } else {
                    assignedToText = `ID: ${task.AssignedTo.Id}`;
                }
            }
        }

        row.innerHTML = `
            <td class="p-3">${task.Id || ''}</td>
            <td class="p-3">${task.Subject || ''}</td>
            <td class="p-3">${task.Project ? task.Project.Name : ''}</td>
            <td class="p-3">${task.Status ? task.Status.Name : ''}</td>
            <td class="p-3">${task.Priority ? task.Priority.Name : ''}</td>
            <td class="p-3">${assignedToText}</td>
            <td class="p-3">${formatDate(task.StartDate)}</td>
            <td class="p-3">${formatDate(task.DueDate)}</td>
            <td class="p-3">${task.DoneRatio}%</td>
        `;

        tableBody.appendChild(row);
    });

    // Sağ tık menüsünü ayarla
    setupQaTaskContextMenu();
}

// QA Task verilerini sıralayan fonksiyon
function sortQaTasks(tasks, column, ascending = true) {
    return [...tasks].sort((a, b) => {
        let valueA, valueB;

        // Özel sütunlar için değerleri al
        switch(column) {
            case 'id':
                valueA = parseInt(a.Id);
                valueB = parseInt(b.Id);
                break;
            case 'subject':
                valueA = a.Subject || '';
                valueB = b.Subject || '';
                break;
            case 'project':
                valueA = a.Project ? a.Project.Name : '';
                valueB = b.Project ? b.Project.Name : '';
                break;
            case 'status':
                valueA = a.Status ? a.Status.Name : '';
                valueB = b.Status ? b.Status.Name : '';
                break;
            case 'priority':
                valueA = a.Priority ? a.Priority.Name : '';
                valueB = b.Priority ? b.Priority.Name : '';
                break;
            case 'assignedTo':
                // Atanan kişi bilgisini düzenle
                if (a.AssignedTo) {
                    if (a.AssignedTo.Name) {
                        valueA = a.AssignedTo.Name;
                    } else {
                        // Kullanıcı ID'sine göre qaTaskUsers içinden bul
                        const assignedUserA = qaTaskUsers.find(user => user.Id === a.AssignedTo.Id);
                        if (assignedUserA) {
                            valueA = assignedUserA.FirstName && assignedUserA.LastName ?
                                `${assignedUserA.FirstName} ${assignedUserA.LastName}` :
                                assignedUserA.Login;
                        } else {
                            valueA = `ID: ${a.AssignedTo.Id}`;
                        }
                    }
                } else {
                    valueA = '';
                }

                if (b.AssignedTo) {
                    if (b.AssignedTo.Name) {
                        valueB = b.AssignedTo.Name;
                    } else {
                        // Kullanıcı ID'sine göre qaTaskUsers içinden bul
                        const assignedUserB = qaTaskUsers.find(user => user.Id === b.AssignedTo.Id);
                        if (assignedUserB) {
                            valueB = assignedUserB.FirstName && assignedUserB.LastName ?
                                `${assignedUserB.FirstName} ${assignedUserB.LastName}` :
                                assignedUserB.Login;
                        } else {
                            valueB = `ID: ${b.AssignedTo.Id}`;
                        }
                    }
                } else {
                    valueB = '';
                }
                break;
            case 'startDate':
                valueA = a.StartDate ? new Date(a.StartDate) : new Date(0);
                valueB = b.StartDate ? new Date(b.StartDate) : new Date(0);
                break;
            case 'dueDate':
                valueA = a.DueDate ? new Date(a.DueDate) : new Date(0);
                valueB = b.DueDate ? new Date(b.DueDate) : new Date(0);
                break;
            case 'doneRatio':
                valueA = a.DoneRatio || 0;
                valueB = b.DoneRatio || 0;
                break;
            default:
                valueA = a[column] || '';
                valueB = b[column] || '';
        }

        // Sayısal değerler için karşılaştırma
        if (typeof valueA === 'number' && typeof valueB === 'number') {
            return ascending ? valueA - valueB : valueB - valueA;
        }

        // Tarih değerleri için karşılaştırma
        if (valueA instanceof Date && valueB instanceof Date) {
            return ascending ? valueA - valueB : valueB - valueA;
        }

        // Metin değerleri için karşılaştırma
        const textA = String(valueA).toLowerCase();
        const textB = String(valueB).toLowerCase();
        return ascending ? textA.localeCompare(textB) : textB.localeCompare(textA);
    });
}

// QA Task arama fonksiyonu
function searchQaTasks() {
    const searchText = document.getElementById('qaTaskSearch').value.toLowerCase();

    if (!qaTasks || qaTasks.length === 0) {
        return;
    }

    const filteredTasks = qaTasks.filter(task => {
        // Tüm alanlarda arama yap
        return (
            (task.Id && task.Id.toString().includes(searchText)) ||
            (task.Subject && task.Subject.toLowerCase().includes(searchText)) ||
            (task.Project && task.Project.Name && task.Project.Name.toLowerCase().includes(searchText)) ||
            (task.Status && task.Status.Name && task.Status.Name.toLowerCase().includes(searchText)) ||
            (task.Priority && task.Priority.Name && task.Priority.Name.toLowerCase().includes(searchText)) ||
            (task.AssignedTo && (
                (task.AssignedTo.Name && task.AssignedTo.Name.toLowerCase().includes(searchText)) ||
                (qaTaskUsers.some(user => user.Id === task.AssignedTo.Id && (
                    (user.FirstName && user.FirstName.toLowerCase().includes(searchText)) ||
                    (user.LastName && user.LastName.toLowerCase().includes(searchText)) ||
                    (user.Login && user.Login.toLowerCase().includes(searchText))
                )))
            )) ||
            (task.Description && task.Description.toLowerCase().includes(searchText))
        );
    });

    displayQaTasks(filteredTasks);
}

// QA Task context menüsünü ayarlayan fonksiyon
function setupQaTaskContextMenu() {
    const tableBody = document.getElementById('qaTaskTableBody');
    const contextMenu = document.getElementById('qaTaskContextMenu');

    if (!tableBody || !contextMenu) return;

    // Daha önce event listener eklenip eklenmediğini kontrol et
    if (tableBody.getAttribute('data-context-menu-setup') === 'true') {
        return; // Zaten kurulmuş, tekrar kurma
    }

    // Sadece admin kullanıcılar için context menüyü göster
    if (userRole !== 'admin') {
        return; // Admin değilse context menüyü kurma
    }

    // Sağ tık olayı
    tableBody.addEventListener('contextmenu', (e) => {
        e.preventDefault();

        const clickedRow = e.target.closest('tr');
        if (!clickedRow || !clickedRow.hasAttribute('data-id')) return;

        const taskId = clickedRow.getAttribute('data-id');
        // Find the task by ID instead of index
        currentQaTask = qaTasks.find(task => task.Id.toString() === taskId);

        // Bağlam menüsünü konumlandır
        positionContextMenu(contextMenu, e.clientX, e.clientY);
        contextMenu.classList.remove('hidden');
    });

    // Sayfa herhangi bir yerine tıklandığında bağlam menüsünü gizle
    document.addEventListener('click', (e) => {
        if (!contextMenu.contains(e.target)) {
            contextMenu.classList.add('hidden');
        }
    });

    // Düzenle butonuna tıklandığında
    const editButton = document.getElementById('qaTaskContextEdit');
    const editClone = editButton.cloneNode(true);
    editButton.parentNode.replaceChild(editClone, editButton);
    editClone.addEventListener('click', () => {
        openQaTaskEditModal(currentQaTask);
        contextMenu.classList.add('hidden');
    });

    // Yorum ekle butonuna tıklandığında
    const commentButton = document.getElementById('qaTaskContextComment');
    const commentClone = commentButton.cloneNode(true);
    commentButton.parentNode.replaceChild(commentClone, commentButton);
    commentClone.addEventListener('click', () => {
        openQaTaskCommentModal(currentQaTask);
        contextMenu.classList.add('hidden');
    });

    // Redmine'da aç butonuna tıklandığında
    const openRedmineButton = document.getElementById('qaTaskContextOpenRedmine');
    const openRedmineClone = openRedmineButton.cloneNode(true);
    openRedmineButton.parentNode.replaceChild(openRedmineClone, openRedmineButton);
    openRedmineClone.addEventListener('click', () => {
        openTaskInRedmine(currentQaTask.Id);
        contextMenu.classList.add('hidden');
    });

    // Tüm listeyi yenile butonuna tıklandığında
    const refreshButton = document.getElementById('qaTaskContextRefresh');
    const refreshClone = refreshButton.cloneNode(true);
    refreshButton.parentNode.replaceChild(refreshClone, refreshButton);
    refreshClone.addEventListener('click', () => {
        showNotification('Task listesi yenileniyor...', 'info');
        loadQaTasks();
        contextMenu.classList.add('hidden');
    });

    // Durum değiştirme alt menüsünü doldur
    loadQaTaskStatuses();

    // Kurulduğunu işaretle
    tableBody.setAttribute('data-context-menu-setup', 'true');
}

// QA Task durumlarını yükleyen fonksiyon
function loadQaTaskStatuses() {
    postMessage({
        type: 'getQaTaskStatuses'
    });
}

// QA Task önceliklerini yükleyen fonksiyon
function loadQaTaskPriorities() {
    postMessage({
        type: 'getQaTaskPriorities'
    });
}

// QA Task kullanıcılarını yükleyen fonksiyon
function loadQaTaskUsers() {
    postMessage({
        type: 'getQaTaskUsers'
    });
}

// QA Task durumlarını alt menüye ekleyen fonksiyon
function displayQaTaskStatuses(statuses) {
    qaTaskStatuses = statuses;
    const statusSubmenu = document.getElementById('qaTaskStatusSubmenu');
    statusSubmenu.innerHTML = '';

    statuses.forEach(status => {
        const item = document.createElement('li');
        item.className = 'px-4 py-2 hover:bg-gray-700 cursor-pointer';
        item.textContent = status.Name;
        item.addEventListener('click', () => {
            // Durum güncellemesi yaparken listeyi yenilemiyoruz
            updateQaTaskStatus(currentQaTask.Id, status.Id, false);
        });
        statusSubmenu.appendChild(item);
    });
}

// QA Task kullanıcılarını işleyen fonksiyon
function displayQaTaskUsers(users) {
    // Kullanıcıları global değişkene kaydet
    qaTaskUsers = users;

    // Konsola kullanıcıların yapısını yazdır (debug için)
    
}

// QA Task durumunu güncelleyen fonksiyon
function updateQaTaskStatus(taskId, statusId, refreshList = false) {
    postMessage({
        type: 'updateQaTaskStatus',
        data: {
            taskId: taskId,
            statusId: statusId
        },
        refreshList: refreshList
    });
}

// QA Task düzenleme modalını açan fonksiyon
function openQaTaskEditModal(task) {
    // Önce gerekli verileri yükle
    if (qaTaskStatuses.length === 0) loadQaTaskStatuses();
    if (qaTaskPriorities.length === 0) loadQaTaskPriorities();
    if (qaTaskUsers.length === 0) loadQaTaskUsers();

    const modal = document.getElementById('qaTaskEditModal');

    // Form alanlarını doldur
    document.getElementById('qaTaskEditId').value = task.Id;
    document.getElementById('qaTaskEditSubject').value = task.Subject || '';

    // Durum seçeneklerini doldur
    const statusSelect = document.getElementById('qaTaskEditStatus');
    statusSelect.innerHTML = '';
    qaTaskStatuses.forEach(status => {
        const option = document.createElement('option');
        option.value = status.Id;
        option.textContent = status.Name;
        if (task.Status && task.Status.Id === status.Id) {
            option.selected = true;
        }
        statusSelect.appendChild(option);
    });

    // Öncelik seçeneklerini doldur
    const prioritySelect = document.getElementById('qaTaskEditPriority');
    prioritySelect.innerHTML = '';
    qaTaskPriorities.forEach(priority => {
        const option = document.createElement('option');
        option.value = priority.Id;
        option.textContent = priority.Name;
        if (task.Priority && task.Priority.Id === priority.Id) {
            option.selected = true;
        }
        prioritySelect.appendChild(option);
    });

    // Atanan kişi seçeneklerini doldur
    const assignedToSelect = document.getElementById('qaTaskEditAssignedTo');
    assignedToSelect.innerHTML = '';

    // Boş seçenek ekle
    const emptyOption = document.createElement('option');
    emptyOption.value = '';
    emptyOption.textContent = 'Seçiniz...';
    assignedToSelect.appendChild(emptyOption);

    // Kullanıcıları ekle
    qaTaskUsers.forEach(user => {
        // Kullanıcı adı ve ID'si kontrolü
        if (!user.Login || !user.Id) {
            
            return;
        }

        const option = document.createElement('option');
        option.value = user.Id;
        option.textContent = user.FirstName && user.LastName ?
            `${user.FirstName} ${user.LastName}` :
            user.Login;

        if (task.AssignedTo && task.AssignedTo.Id === user.Id) {
            option.selected = true;
        }
        assignedToSelect.appendChild(option);
    });

    // Tamamlanma oranını ayarla
    document.getElementById('qaTaskEditDoneRatio').value = task.DoneRatio || 0;

    // Tarihleri ayarla - Timezone sorununu çözmek için UTC offset ekliyoruz
    if (task.StartDate) {
        const startDate = new Date(task.StartDate);
        // Timezone farkını hesaba katarak doğru tarihi elde et
        // getTimezoneOffset() negatif değer döndürür, bu yüzden çıkarmak yerine ekliyoruz
        const localStartDate = new Date(startDate.getTime() - startDate.getTimezoneOffset() * 60000);
        document.getElementById('qaTaskEditStartDate').value = localStartDate.toISOString().split('T')[0];
    } else {
        document.getElementById('qaTaskEditStartDate').value = '';
    }

    if (task.DueDate) {
        const dueDate = new Date(task.DueDate);
        // Timezone farkını hesaba katarak doğru tarihi elde et
        // getTimezoneOffset() negatif değer döndürür, bu yüzden çıkarmak yerine ekliyoruz
        const localDueDate = new Date(dueDate.getTime() - dueDate.getTimezoneOffset() * 60000);
        document.getElementById('qaTaskEditDueDate').value = localDueDate.toISOString().split('T')[0];
    } else {
        document.getElementById('qaTaskEditDueDate').value = '';
    }

    // Açıklamayı ayarla - HTML etiketlerini ve özel karakterleri düzgün göstermek için
    const descriptionText = task.Description || '';
    // HTML kodlarını decode et
    const textarea = document.getElementById('qaTaskEditDescription');
    textarea.value = decodeHTMLEntities(descriptionText);

    // Modalı göster
    modal.classList.remove('hidden');

    // Önce mevcut event listener'ları kaldır
    const cancelButton = document.getElementById('qaTaskEditCancel');
    const saveButton = document.getElementById('qaTaskEditSave');

    // Eski event listener'ları kaldır
    const cancelClone = cancelButton.cloneNode(true);
    const saveClone = saveButton.cloneNode(true);

    cancelButton.parentNode.replaceChild(cancelClone, cancelButton);
    saveButton.parentNode.replaceChild(saveClone, saveButton);

    // Yeni event listener'ları ekle
    cancelClone.addEventListener('click', () => {
        modal.classList.add('hidden');
    });

    saveClone.addEventListener('click', () => {
        saveQaTaskEdit();
    });
}

// QA Task düzenlemesini kaydeden fonksiyon
function saveQaTaskEdit() {
    const taskId = document.getElementById('qaTaskEditId').value;
    const subject = document.getElementById('qaTaskEditSubject').value;
    const statusId = document.getElementById('qaTaskEditStatus').value;
    const priorityId = document.getElementById('qaTaskEditPriority').value;
    const assignedToId = document.getElementById('qaTaskEditAssignedTo').value;
    const doneRatio = document.getElementById('qaTaskEditDoneRatio').value;
    const startDate = document.getElementById('qaTaskEditStartDate').value;
    const dueDate = document.getElementById('qaTaskEditDueDate').value;

    // Açıklama metnini al ve HTML kodlarını koru
    const description = document.getElementById('qaTaskEditDescription').value;

    // Verileri gönder
    postMessage({
        type: 'updateQaTask',
        data: {
            id: taskId,
            subject: subject,
            statusId: statusId,
            priorityId: priorityId,
            assignedToId: assignedToId,
            doneRatio: doneRatio,
            startDate: startDate,
            dueDate: dueDate,
            description: description
        },
        refreshList: false
    });

    // Modalı kapat
    document.getElementById('qaTaskEditModal').classList.add('hidden');
}

// QA Task yorum modalını açan fonksiyon
function openQaTaskCommentModal(task) {
    const modal = document.getElementById('qaTaskCommentModal');
    document.getElementById('qaTaskCommentId').value = task.Id;
    document.getElementById('qaTaskCommentText').value = '';

    // Modalı göster
    modal.classList.remove('hidden');

    // Önce mevcut event listener'ları kaldır
    const cancelButton = document.getElementById('qaTaskCommentCancel');
    const saveButton = document.getElementById('qaTaskCommentSave');

    // Eski event listener'ları kaldır
    const cancelClone = cancelButton.cloneNode(true);
    const saveClone = saveButton.cloneNode(true);

    cancelButton.parentNode.replaceChild(cancelClone, cancelButton);
    saveButton.parentNode.replaceChild(saveClone, saveButton);

    // Yeni event listener'ları ekle
    cancelClone.addEventListener('click', () => {
        modal.classList.add('hidden');
    });

    saveClone.addEventListener('click', () => {
        saveQaTaskComment();
    });
}

// QA Task yorumunu kaydeden fonksiyon
function saveQaTaskComment() {
    const taskId = document.getElementById('qaTaskCommentId').value;
    const commentText = document.getElementById('qaTaskCommentText').value;

    if (!commentText.trim()) {
        showNotification('Lütfen bir yorum yazın', 'warning');
        return;
    }

    postMessage({
        type: 'addQaTaskComment',
        data: {
            taskId: taskId,
            comment: commentText
        },
        refreshList: false
    });

    // Modalı kapat
    document.getElementById('qaTaskCommentModal').classList.add('hidden');
}

// Yükleniyor göstergesini gösteren yardımcı fonksiyon
function showLoadingIndicator(tableBodyId) {
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) return;

    tableBody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center py-4 text-gray-400">
                <i class="fas fa-spinner fa-spin mr-2"></i> Veriler yükleniyor...
            </td>
        </tr>
    `;
}

// Yükleniyor göstergesini gizleyen yardımcı fonksiyon
function hideLoadingIndicator(tableBodyId) {
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) return;

    // Yükleniyor mesajını temizle
    if (tableBody.innerHTML.includes('Veriler yükleniyor')) {
        tableBody.innerHTML = '';
    }
}

// QA Task ile ilgili event listener'ları ayarlayan fonksiyon
function setupQaTaskEventListeners() {
    // QA Task arama kutusu
    const qaTaskSearchInput = document.getElementById('qaTaskSearch');
    if (qaTaskSearchInput) {
        qaTaskSearchInput.addEventListener('input', () => {
            searchQaTasks();
        });
    }

    // QA Task yenile butonu - Açıkça yenileme isteği gönderir
    const refreshQaTaskButton = document.getElementById('refreshQaTaskButton');
    if (refreshQaTaskButton) {
        refreshQaTaskButton.addEventListener('click', () => {
            // Yenileme işlemi sırasında bildirim göster
            showNotification('Task listesi yenileniyor...', 'info');
            loadQaTasks();
        });
    }

    // QA Task tablosu sıralama
    const qaTaskTable = document.getElementById('qaTaskTable');
    if (qaTaskTable) {
        const headers = qaTaskTable.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.addEventListener('click', () => {
                const column = header.getAttribute('data-sort');
                if (qaTasksSort.column === column) {
                    // Aynı sütuna tıklandığında sıralama yönünü değiştir
                    qaTasksSort.direction = qaTasksSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    // Farklı sütuna tıklandığında sütunu güncelle ve artan sıralama yap
                    qaTasksSort.column = column;
                    qaTasksSort.direction = 'asc';
                }

                // Tabloyu yeniden oluştur
                displayQaTasks(qaTasks);
            });
        });
    }

    // Studio QA Tasks arama kutusu
    const studioQATasksSearchInput = document.getElementById('studioQATasksSearch');
    if (studioQATasksSearchInput) {
        studioQATasksSearchInput.addEventListener('input', () => {
            searchStudioQATasks();
        });
    }

    // Studio QA Tasks yenile butonu
    const refreshStudioQATasksButton = document.getElementById('refreshStudioQATasksButton');
    if (refreshStudioQATasksButton) {
        refreshStudioQATasksButton.addEventListener('click', () => {
            showNotification('Studio QA Task listesi yenileniyor...', 'info');
            loadStudioQATasks();
        });
    }

    // Studio QA Tasks tablosu sıralama
    const studioQATasksTable = document.getElementById('studioQATasksTable');
    if (studioQATasksTable) {
        const headers = studioQATasksTable.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.addEventListener('click', () => {
                const column = header.getAttribute('data-sort');
                if (studioQATasksSort.column === column) {
                    // Aynı sütuna tıklandığında sıralama yönünü değiştir
                    studioQATasksSort.direction = studioQATasksSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    // Farklı sütuna tıklandığında sütunu güncelle ve artan sıralama yap
                    studioQATasksSort.column = column;
                    studioQATasksSort.direction = 'asc';
                }

                // Tabloyu yeniden oluştur
                displayStudioQATasks(studioQATasks);
            });
        });
    }
}

// All Tasks verilerini yükleyen fonksiyon
function loadAllTasks() {
    showLoadingIndicator('allTasksTableBody');

    postMessage({
        type: 'getAllTasks'
    });
}

// Studio QA Tasks verilerini yükleyen fonksiyon
function loadStudioQATasks() {
    showLoadingIndicator('studioQATasksTableBody');

    postMessage({
        type: 'getStudioQATasks'
    });
}

// Studio QA Tasks verilerini tabloya ekleyen fonksiyon
function displayStudioQATasks(tasks) {
    // Eğer tasks parametresi verilmişse, studioQATasks değişkenini güncelle
    if (tasks) {
        studioQATasks = tasks;
    }

    const tableBody = document.getElementById('studioQATasksTableBody');
    tableBody.innerHTML = '';

    // Eğer hiç task yoksa veya tasks boş bir array ise
    if (!studioQATasks || studioQATasks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4 text-gray-400">
                    Herhangi bir task bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Sıralama yap
    const sortedTasks = sortStudioQATasks(studioQATasks, studioQATasksSort.column, studioQATasksSort.direction === 'asc');

    // Tabloya ekle
    sortedTasks.forEach((task, index) => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-700 transition-colors cursor-pointer';
        row.setAttribute('data-id', task.Id);
        row.setAttribute('data-index', index);

        // Çift tıklama olayı ekle
        row.addEventListener('dblclick', () => {
            openTaskInRedmine(task.Id);
        });

        // We don't need assignedToText anymore as it's not in the table

        // Get Game Name from custom field with ID 5
        let gameName = '';
        if (task.CustomFields && task.CustomFields.length > 0) {
            const gameNameField = task.CustomFields.find(field => field.Id === 30);
            if (gameNameField && gameNameField.Value) {
                gameName = gameNameField.Values[0].Info;
            }
        }

        row.innerHTML = `
            <td class="p-3 hidden">${task.Id || ''}</td>
            <td class="p-3">${gameName || '-'}</td>
            <td class="p-3">${task.Author ? task.Author.Name : '-'}</td>
            <td class="p-3">${task.Project ? task.Project.Name : '-'}</td>
            <td class="p-3">${task.Status ? task.Status.Name : '-'}</td>
            <td class="p-3">${formatDate(task.StartDate)}</td>
            <td class="p-3">${task.Subject || '-'}</td>
        `;

        tableBody.appendChild(row);
    });
}

// Studio QA Tasks verilerini sıralayan fonksiyon
function sortStudioQATasks(tasks, column, ascending = true) {
    return [...tasks].sort((a, b) => {
        let valueA, valueB;

        // Sıralama sütununa göre değerleri al
        switch (column) {
            case 'id':
                valueA = a.Id;
                valueB = b.Id;
                break;
            case 'subject':
                valueA = a.Subject || '';
                valueB = b.Subject || '';
                break;
            case 'project':
                valueA = a.Project ? a.Project.Name : '';
                valueB = b.Project ? b.Project.Name : '';
                break;
            case 'status':
                valueA = a.Status ? a.Status.Name : '';
                valueB = b.Status ? b.Status.Name : '';
                break;
            case 'author':
                valueA = a.Author ? a.Author.Name : '';
                valueB = b.Author ? b.Author.Name : '';
                break;
            case 'customField':
                // Get Game Name from custom field with ID 5
                let gameNameA = '';
                if (a.CustomFields && a.CustomFields.length > 0) {
                    const gameNameField = a.CustomFields.find(field => field.Id === 30);
                    if (gameNameField && gameNameField.Value) {
                        gameNameA = gameNameField.Values[0].Info;
                    }
                }

                let gameNameB = '';
                if (b.CustomFields && b.CustomFields.length > 0) {
                    const gameNameField = b.CustomFields.find(field => field.Id === 30);
                    if (gameNameField && gameNameField.Value) {
                        gameNameB = gameNameField.Values[0].Info;
                    }
                }

                valueA = gameNameA;
                valueB = gameNameB;
                break;
            case 'startDate':
                valueA = a.StartDate ? new Date(a.StartDate) : new Date(0);
                valueB = b.StartDate ? new Date(b.StartDate) : new Date(0);
                break;
            default:
                valueA = a.Id;
                valueB = b.Id;
        }

        // Sayısal değerler için
        if (typeof valueA === 'number' && typeof valueB === 'number') {
            return ascending ? valueA - valueB : valueB - valueA;
        }

        // Tarih değerleri için
        if (valueA instanceof Date && valueB instanceof Date) {
            return ascending ? valueA - valueB : valueB - valueA;
        }

        // Metin değerleri için
        if (typeof valueA === 'string' && typeof valueB === 'string') {
            return ascending ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
        }

        // Varsayılan sıralama
        return ascending ? (valueA > valueB ? 1 : -1) : (valueA < valueB ? 1 : -1);
    });
}

// Studio QA Tasks arama fonksiyonu
function searchStudioQATasks() {
    const searchText = document.getElementById('studioQATasksSearch').value.toLowerCase();

    // Eğer arama metni boşsa, tüm verileri göster
    if (!searchText.trim()) {
        // Eğer studioQATasks boşsa, verileri yeniden yükle
        if (!studioQATasks || studioQATasks.length === 0) {
            loadStudioQATasks();
            return;
        }
        displayStudioQATasks(studioQATasks);
        return;
    }

    // Eğer studioQATasks boşsa, verileri yeniden yükle
    if (!studioQATasks || studioQATasks.length === 0) {
        loadStudioQATasks();
        return;
    }

    const filteredTasks = studioQATasks.filter(task => {
        // Get Game Name from custom field with ID 5
        let gameName = '';
        if (task.CustomFields && task.CustomFields.length > 0) {
            const gameNameField = task.CustomFields.find(field => field.Id === 30);
            if (gameNameField && gameNameField.Value) {
                gameName = gameNameField.Values[0].Info;
            }
        }

        return (
            (task.Id && task.Id.toString().includes(searchText)) ||
            (task.Subject && task.Subject.toLowerCase().includes(searchText)) ||
            (task.Project && task.Project.Name && task.Project.Name.toLowerCase().includes(searchText)) ||
            (task.Status && task.Status.Name && task.Status.Name.toLowerCase().includes(searchText)) ||
            (task.Author && task.Author.Name && task.Author.Name.toLowerCase().includes(searchText)) ||
            (gameName && gameName.toLowerCase().includes(searchText))
        );
    });

    // Arama sonuçlarını göster
    displayStudioQATasks(filteredTasks);

    // Eğer sonuç bulunamadıysa bildirim göster
    if (filteredTasks.length === 0) {
        showNotification('Arama kriterlerine uygun task bulunamadı.', 'info');
    }
}

// Studio QA Tasks durumunu güncelleyen fonksiyon
function updateStudioQATaskStatus(taskId, statusId, refreshList = false) {
    postMessage({
        type: 'updateStudioQATaskStatus',
        data: {
            taskId: taskId,
            statusId: statusId
        },
        refreshList: refreshList
    });
}

// All Tasks verilerini tabloya ekleyen fonksiyon
function displayAllTasks(tasks) {
    allTasks = tasks;
    const tableBody = document.getElementById('allTasksTableBody');
    tableBody.innerHTML = '';

    if (allTasks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4 text-gray-400">
                    Herhangi bir task bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Sıralama yap
    const sortedTasks = sortAllTasks(allTasks, allTasksSort.column, allTasksSort.direction === 'asc');

    sortedTasks.forEach(task => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-600 transition-colors cursor-pointer';
        row.setAttribute('data-id', task.Id);

        // Atanan kişi bilgisini düzenle
        let assignedToText = 'Atanmamış';
        if (task.AssignedTo && task.AssignedTo.Name) {
            assignedToText = task.AssignedTo.Name;
        }

        row.innerHTML = `
            <td class="p-3">${task.Id || ''}</td>
            <td class="p-3">${task.Subject || ''}</td>
            <td class="p-3">${task.Project ? task.Project.Name : ''}</td>
            <td class="p-3">${task.Status ? task.Status.Name : ''}</td>
            <td class="p-3">${task.Priority ? task.Priority.Name : ''}</td>
            <td class="p-3">${assignedToText}</td>
            <td class="p-3">${formatDate(task.StartDate)}</td>
            <td class="p-3">${formatDate(task.DueDate)}</td>
            <td class="p-3">${task.DoneRatio}%</td>
        `;

        // Çift tıklama olayı ekle
        row.addEventListener('dblclick', () => {
            openTaskInRedmine(task.Id);
        });

        tableBody.appendChild(row);
    });

    // Sağ tık menüsünü ayarla
    setupAllTasksContextMenu();
}

// All Tasks verilerini sıralayan fonksiyon
function sortAllTasks(tasks, column, ascending = true) {
    return [...tasks].sort((a, b) => {
        let valueA, valueB;

        switch (column) {
            case 'id':
                valueA = a.Id || 0;
                valueB = b.Id || 0;
                break;
            case 'subject':
                valueA = a.Subject || '';
                valueB = b.Subject || '';
                break;
            case 'project':
                valueA = a.Project ? a.Project.Name : '';
                valueB = b.Project ? b.Project.Name : '';
                break;
            case 'status':
                valueA = a.Status ? a.Status.Name : '';
                valueB = b.Status ? b.Status.Name : '';
                break;
            case 'priority':
                valueA = a.Priority ? a.Priority.Name : '';
                valueB = b.Priority ? b.Priority.Name : '';
                break;
            case 'assignedTo':
                valueA = a.AssignedTo ? a.AssignedTo.Name : '';
                valueB = b.AssignedTo ? b.AssignedTo.Name : '';
                break;
            case 'startDate':
                valueA = a.StartDate ? new Date(a.StartDate) : new Date(0);
                valueB = b.StartDate ? new Date(b.StartDate) : new Date(0);
                break;
            case 'dueDate':
                valueA = a.DueDate ? new Date(a.DueDate) : new Date(0);
                valueB = b.DueDate ? new Date(b.DueDate) : new Date(0);
                break;
            case 'doneRatio':
                valueA = a.DoneRatio || 0;
                valueB = b.DoneRatio || 0;
                break;
            default:
                valueA = a.Id || 0;
                valueB = b.Id || 0;
        }

        // Sayısal değerler için
        if (typeof valueA === 'number' && typeof valueB === 'number') {
            return ascending ? valueA - valueB : valueB - valueA;
        }

        // Tarih değerleri için
        if (valueA instanceof Date && valueB instanceof Date) {
            return ascending ? valueA - valueB : valueB - valueA;
        }

        // Metin değerleri için
        const textA = String(valueA).toLowerCase();
        const textB = String(valueB).toLowerCase();
        return ascending ? textA.localeCompare(textB) : textB.localeCompare(textA);
    });
}

// All Tasks arama fonksiyonu
function searchAllTasks() {
    const searchText = document.getElementById('allTasksSearch').value.toLowerCase();
    const filteredTasks = allTasks.filter(task => {
        return (
            (task.Id && task.Id.toString().includes(searchText)) ||
            (task.Subject && task.Subject.toLowerCase().includes(searchText)) ||
            (task.Project && task.Project.Name && task.Project.Name.toLowerCase().includes(searchText)) ||
            (task.Status && task.Status.Name && task.Status.Name.toLowerCase().includes(searchText)) ||
            (task.Priority && task.Priority.Name && task.Priority.Name.toLowerCase().includes(searchText)) ||
            (task.AssignedTo && task.AssignedTo.Name && task.AssignedTo.Name.toLowerCase().includes(searchText))
        );
    });

    displayAllTasks(filteredTasks);
}

// All Tasks context menüsünü ayarlayan fonksiyon
function setupAllTasksContextMenu() {
    const tableBody = document.getElementById('allTasksTableBody');
    const contextMenu = document.getElementById('allTasksContextMenu');

    if (!tableBody || !contextMenu) return;

    // Daha önce event listener eklenip eklenmediğini kontrol et
    if (tableBody.getAttribute('data-context-menu-setup') === 'true') {
        return; // Zaten kurulmuş, tekrar kurma
    }

    // Sağ tık olayı
    tableBody.addEventListener('contextmenu', (e) => {
        e.preventDefault();

        const clickedRow = e.target.closest('tr');
        if (!clickedRow || !clickedRow.hasAttribute('data-id')) return;

        const taskId = clickedRow.getAttribute('data-id');
        // Find the task by ID instead of index
        currentAllTask = allTasks.find(task => task.Id.toString() === taskId);

        // Bağlam menüsünü konumlandır
        positionContextMenu(contextMenu, e.clientX, e.clientY);
        contextMenu.classList.remove('hidden');
    });

    // Sayfa herhangi bir yerine tıklandığında bağlam menüsünü gizle
    document.addEventListener('click', (e) => {
        if (!contextMenu.contains(e.target)) {
            contextMenu.classList.add('hidden');
        }
    });

    // Düzenle butonuna tıklandığında
    const editButton = document.getElementById('allTasksContextEdit');
    const editClone = editButton.cloneNode(true);
    editButton.parentNode.replaceChild(editClone, editButton);
    editClone.addEventListener('click', () => {
        openQaTaskEditModal(currentAllTask); // Aynı modalı kullan
        contextMenu.classList.add('hidden');
    });

    // Yorum ekle butonuna tıklandığında
    const commentButton = document.getElementById('allTasksContextComment');
    const commentClone = commentButton.cloneNode(true);
    commentButton.parentNode.replaceChild(commentClone, commentButton);
    commentClone.addEventListener('click', () => {
        openQaTaskCommentModal(currentAllTask); // Aynı modalı kullan
        contextMenu.classList.add('hidden');
    });

    // Redmine'da aç butonuna tıklandığında
    const openRedmineButton = document.getElementById('allTasksContextOpenRedmine');
    const openRedmineClone = openRedmineButton.cloneNode(true);
    openRedmineButton.parentNode.replaceChild(openRedmineClone, openRedmineButton);
    openRedmineClone.addEventListener('click', () => {
        openTaskInRedmine(currentAllTask.Id);
        contextMenu.classList.add('hidden');
    });

    // Tüm listeyi yenile butonuna tıklandığında
    const refreshButton = document.getElementById('allTasksContextRefresh');
    const refreshClone = refreshButton.cloneNode(true);
    refreshButton.parentNode.replaceChild(refreshClone, refreshButton);
    refreshClone.addEventListener('click', () => {
        showNotification('Task listesi yenileniyor...', 'info');
        loadAllTasks();
        contextMenu.classList.add('hidden');
    });

    // Durum değiştirme alt menüsünü doldur
    loadQaTaskStatuses(); // Aynı durumları kullan

    // Kurulduğunu işaretle
    tableBody.setAttribute('data-context-menu-setup', 'true');
}

// All Tasks durumlarını alt menüye ekleyen fonksiyon
function displayAllTasksStatuses(statuses) {
    const statusSubmenu = document.getElementById('allTasksStatusSubmenu');
    statusSubmenu.innerHTML = '';

    statuses.forEach(status => {
        const item = document.createElement('li');
        item.className = 'px-4 py-2 hover:bg-gray-700 cursor-pointer';
        item.textContent = status.Name;
        item.addEventListener('click', () => {
            // Durum güncellemesi yaparken listeyi yenilemiyoruz
            updateAllTaskStatus(currentAllTask.Id, status.Id, false);
        });
        statusSubmenu.appendChild(item);
    });
}

// All Tasks durumunu güncelleyen fonksiyon
function updateAllTaskStatus(taskId, statusId, refreshList = false) {
    postMessage({
        type: 'updateAllTaskStatus',
        data: {
            taskId: taskId,
            statusId: statusId
        },
        refreshList: refreshList
    });
}

// Task'i Redmine'da açan fonksiyon
function openTaskInRedmine(taskId) {
    if (!taskId) return;

    const redmineUrl = 'https://project.joygame.com/issues/';
    const fullUrl = redmineUrl + taskId;

    // Tarayıcıda aç
    postMessage({
        type: 'openUrl',
        url: fullUrl
    });

    showNotification(`Task #${taskId} tarayıcıda açılıyor...`, 'info');
}

// All Tasks ile ilgili event listener'ları ayarlayan fonksiyon
function setupAllTasksEventListeners() {
    // All Tasks arama kutusu
    const allTasksSearchInput = document.getElementById('allTasksSearch');
    if (allTasksSearchInput) {
        allTasksSearchInput.addEventListener('input', () => {
            searchAllTasks();
        });
    }

    // All Tasks yenile butonu
    const refreshAllTasksButton = document.getElementById('refreshAllTasksButton');
    if (refreshAllTasksButton) {
        refreshAllTasksButton.addEventListener('click', () => {
            showNotification('Task listesi yenileniyor...', 'info');
            loadAllTasks();
        });
    }

    // All Tasks tablosu sıralama
    const allTasksTable = document.getElementById('allTasksTable');
    if (allTasksTable) {
        const headers = allTasksTable.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.addEventListener('click', () => {
                const column = header.getAttribute('data-sort');
                if (allTasksSort.column === column) {
                    allTasksSort.direction = allTasksSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    allTasksSort.column = column;
                    allTasksSort.direction = 'asc';
                }

                displayAllTasks(allTasks);
            });
        });
    }
}

// Ödeme durumunu güncelleme fonksiyonu
function updatePaymentStatus(status) {
    const selectedRows = getSelectedRows();
    const selectedIds = selectedRows.map(row => row.data.id);

    postMessage({
        type: 'updatePaymentStatus',
        data: {
            ids: selectedIds,
            status: status
        }
    });
}

// Otomatik refund işlevi
function startAutoRefund() {
    // Aktif sekme kontrol edilir
    const activeTabContent = document.querySelector('.tab-content:not(.hidden)');
    if (!activeTabContent || activeTabContent.id !== 'payments') {
        showNotification('Otomatik refund sadece Ödemeler sekmesinde çalışır.', 'warning');
        document.getElementById('autoRefund').checked = false;
        return;
    }

    if (autoRefundTimer !== null) {
        
        return;
    }
    
    // Eğer refund işlemi devam ediyorsa beklet
    if (isRefundInProgress) {
        
        wasAutoRefundActiveBeforeRefund = true;
        isAutoRefundActive = true;
        return;
    }

    // Admin yetkisi kontrolü
    if (!adminse()) {
        showNotification('Otomatik refund özelliğini kullanmak için admin yetkisi gereklidir.', 'error');
        document.getElementById('autoRefund').checked = false;
        return;
    }

    
    isAutoRefundActive = true;
    
    // İlk olarak hemen çalıştır
    autoRefundProcess();
    
    // Sonra 10 saniyede bir çalıştırmaya ayarla
    autoRefundTimer = setInterval(autoRefundProcess, 10000);
    showNotification('Otomatik refund başlatıldı. Her 10 saniyede bir kontrol yapılacak.', 'info');
}

function stopAutoRefund() {
    if (autoRefundTimer === null) {
        
        return;
    }
    
    
    clearInterval(autoRefundTimer);
    autoRefundTimer = null;
    isAutoRefundActive = false;
    showNotification('Otomatik refund durduruldu.', 'info');
}

function autoRefundProcess() {
    // Eğer Payments sekmesinde değilse işlemi durdur
    const activeTabContent = document.querySelector('.tab-content:not(.hidden)');
    if (!activeTabContent || activeTabContent.id !== 'payments') {
        
        document.getElementById('autoRefund').checked = false;
        stopAutoRefund();
        return;
    }
    
    
    
    // Ödemeleri yenile
    refreshPayments();
    
    // Bekle ve refund işlemini yap
    setTimeout(() => {
    // Eğer interval durdurulmuşsa işlemi yapma (race condition önlemi)
        if (!isAutoRefundActive) return;
        
    // Eğer refund işlemi devam ediyorsa bekle
    if (isRefundInProgress) {
        
        return;
    }
        
        // Refund edilebilecek ödemeleri bul
        const pendingPayments = [];
        const rows = document.querySelectorAll('#paymentsTable tbody tr');
        
        rows.forEach(row => {
            const statusCell = row.querySelector('td:last-child span');
            if (statusCell && statusCell.textContent.trim() === 'Bekliyor') {
                // Veriyi dataset'ten al (daha güvenilir)
                pendingPayments.push({
                    id: Number(row.getAttribute('data-payment-id')),
                    paymentPin: row.getAttribute('data-payment-pin'),
                    bundleId: row.getAttribute('data-bundle-id'),
                    appName: row.getAttribute('data-app-name'),
                    tester: row.getAttribute('data-tester')
                });
            }
        });
        
        // Bekleyen ödeme varsa refund et
        if (pendingPayments.length > 0) {            
            // Seçili satırları loading durumuna geçir
            pendingPayments.forEach(payment => {
                const row = document.querySelector(`tr[data-payment-id="${payment.id}"]`);
                if (row) {
                    const statusCell = row.querySelector('td:last-child span');
                    statusCell.className = 'px-2 py-1 rounded-full text-sm bg-blue-500/20 text-blue-500';
                    statusCell.innerHTML = `
                        <svg class="inline animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        İşleniyor...
                    `;
                }
            });
                  // Refund işlemine başlamadan önce otomatik refund durumunu kaydet
        wasAutoRefundActiveBeforeRefund = isAutoRefundActive;
        
        // Eğer otomatik refund aktifse, duraklat
        if (isAutoRefundActive) {
            clearInterval(autoRefundTimer);
            autoRefundTimer = null;
            
        }
        
        // Refund işlemini başlat
        isRefundInProgress = true;
        postMessage({
                type: 'refundPayments',
                data: { payments: pendingPayments }
        });
            
        showNotification(`${pendingPayments.length} adet bekleyen ödeme refund işlemine alındı.`, 'info');
        } else {
            
        }
    }, 1000); // Ödemelerin yüklenmesi için 1 saniye bekle
}
async function handleLogin() {
    const loginId = document.getElementById('loginId').value;
    const loginPw = document.getElementById('loginPw').value;
    const rememberMe = document.getElementById('remember-me').checked;

    if (!loginId || !loginPw) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-red-500 text-sm mt-2';
        errorDiv.textContent = 'Lütfen tüm alanları doldurun';
        const oldError = document.querySelector('.text-red-500');
        if (oldError) oldError.remove();
        document.querySelector('button[onclick="handleLogin()"]').parentElement.appendChild(errorDiv);
        setTimeout(() => errorDiv.remove(), 3000);
        return;
    }

    const loginButton = document.querySelector('button[onclick="handleLogin()"]');
    loginButton.disabled = true;
    loginButton.textContent = 'Giriş Yapılıyor...';

    postMessage({
        type: 'login',
        data: {
            id: loginId,
            password: loginPw,
            rememberMe: rememberMe
        }
    });
}
// Sonuç geldiğinde tabloyu güncellemek için yeni bir fonksiyon
function updateRefundStatus(paymentId, status, message = '') {
    const row = document.querySelector(`tr[data-payment-id="${paymentId}"]`);
    if (!row) {
        console.error('Payment row not found:', paymentId);
        return;
    }

    const statusCell = row.querySelector('td:last-child span');
    if (status === 'success') {
        statusCell.className = 'px-2 py-1 rounded-full text-sm bg-green-500/20 text-green-500';
        statusCell.textContent = 'Tamamlandı';
    } else {
        statusCell.className = 'px-2 py-1 rounded-full text-sm bg-red-500/20 text-red-500';
        statusCell.textContent = 'Hata: ' + message;
    }
    
    // Eğer bu refund işlemi tamamlanmış ve başka refund işlemi yoksa refund işlemini tamamla
    checkAllRefundOperationsComplete();
}


function checkAllRefundOperationsComplete() {
    // Bekliyor veya İşleniyor durumunda olan ödeme var mı kontrol et
    const pendingOperations = document.querySelectorAll('#paymentsTable tbody tr td:last-child span');
    let hasOngoingOperations = false;
    
    pendingOperations.forEach(statusSpan => {
        const text = statusSpan.textContent.trim();
        if (text === 'Bekliyor' || text.includes('İşleniyor')) {
            hasOngoingOperations = true;
        }
    });
    
    // Eğer devam eden işlem yoksa, refund işlemlerinin tamamlandığını belirt
    if (!hasOngoingOperations) {
        
        isRefundInProgress = false;
        
        // Eğer refund işlemi başlamadan önce otomatik refund aktifse, tekrar başlat
        if (wasAutoRefundActiveBeforeRefund) {
            
            wasAutoRefundActiveBeforeRefund = false;
            startAutoRefund();
        }
    }
}

function purgeMediaNova() {
    showConfirmationModal(
        'MediaNova önbelleğini temizlemek istediğinizden emin misiniz?',
        async () => {
            await chrome.webview.postMessage({
                type: 'purgeMediaNova'
            });
        }
    );
}

// Admin durumuna göre grid layout'u güncelle
function updateGridLayoutBasedOnAdmin() {
    const adminCommands = document.getElementById('admin-commands');
    const mainGrid = document.querySelector('#actions > div');

    if (!adminCommands || !mainGrid) return;

    if (adminse()) {
        // Admin ise 4 sütun göster
        adminCommands.style.display = 'block';
        mainGrid.classList.remove('non-admin');
    } else {
        // Admin değilse admin paneli gizle ve 3 sütun yap
        adminCommands.style.display = 'none';
        mainGrid.classList.add('non-admin');
    }
}

// Cihaz durumu ve OS seçimine göre UI'yi güncelle
function updateUIBasedOnDeviceStatus() {
    // İlgili bölümleri seç
    const logSystem = document.getElementById('log-system');
    const deviceFunctions = document.getElementById('device-functions');
    const appFunctions = document.getElementById('app-functions');

    // Admin panel kontrolü ve grid ayarlaması
    updateGridLayoutBasedOnAdmin();

    // Cihaz bağlı değilse, tüm bölümleri devre dışı bırak
    const shouldDisable = !isDeviceConnected;

    if (shouldDisable) {
        // Cihaz bağlı değilse tüm bölümleri devre dışı bırak
        if (logSystem) {
            toggleSectionDisabled(logSystem, true);
        }

        if (deviceFunctions) {
            toggleSectionDisabled(deviceFunctions, true);
        }

        if (appFunctions) {
            toggleSectionDisabled(appFunctions, true);
        }

        // Kullanıcıya bildirim göster - sadece bir kez
        if (!disconnectionNotificationShown) {
            if (currentOS === 'AOS') {
                showNotification('Android cihaz bağlı değil. Bazı özellikler devre dışı bırakıldı.', 'warning');
            } else if (currentOS === 'IOS') {
                showNotification('iOS cihaz bağlı değil. Bazı özellikler devre dışı bırakıldı.', 'warning');
            }
            disconnectionNotificationShown = true; // Bildirimin gösterildiğini işaretle
        }
    } else {
        // Bağlantı kesildi bildirim bayrağını sıfırla
        disconnectionNotificationShown = false;

        // Cihaz bağlı ve Android ise tüm özellikleri etkinleştir
        if (currentOS === 'AOS') {
            if (logSystem) {
                toggleSectionDisabled(logSystem, false);
            }

            if (deviceFunctions) {
                toggleSectionDisabled(deviceFunctions, false);
            }

            if (appFunctions) {
                toggleSectionDisabled(appFunctions, false);
            }

            // Bağlantı bildirimi daha önce gösterilmediyse göster
            if (!connectionNotificationShown) {
                showNotification('Android cihaz bağlandı. Tüm özellikler etkinleştirildi.', 'success');
                connectionNotificationShown = true;
            }
        }
        // Cihaz bağlı ve iOS ise sadece belirli özellikleri etkinleştir
        else if (currentOS === 'IOS') {
            // Önce tüm bölümleri devre dışı bırak
            if (logSystem) {
                toggleSectionDisabled(logSystem, true);

                // Sadece "Log Kaydını Başlat" düğmesini etkinleştir
                const logButton = document.getElementById('startLogRecordingButton');
                if (logButton) {
                    logButton.disabled = false;
                    logButton.classList.remove('cursor-not-allowed');
                }

                // Log notu alanını etkinleştir
                const logNote = document.getElementById('logNote');
                if (logNote) {
                    logNote.disabled = false;
                    logNote.classList.remove('cursor-not-allowed');
                }

                // Log timer alanını görünür yap
                const logTimer = document.getElementById('logTimer');
                if (logTimer) {
                    logTimer.style.opacity = '1';
                }

                // Bölümün tamamını görünür yap
                logSystem.style.opacity = '1';
            }

            if (deviceFunctions) {
                toggleSectionDisabled(deviceFunctions, true);

                // Sadece "Cihazı Yeniden Başlat" düğmesini etkinleştir
                const restartButton = deviceFunctions.querySelector('button[onclick="restartDevice()"]');
                if (restartButton) {
                    restartButton.disabled = false;
                    restartButton.classList.remove('cursor-not-allowed');
                }

                // Sadece "Cihaz Bilgilerini Kopyala" düğmesini etkinleştir
                const copyButton = deviceFunctions.querySelector('#device-functions > div.mt-3 > button');
                if (copyButton) {
                    copyButton.disabled = false;
                    copyButton.classList.remove('cursor-not-allowed');
                }


                // Cihaz bilgileri bölümünü görünür yap
                const deviceInfo = document.getElementById('device-info');
                if (deviceInfo) {
                    deviceInfo.style.opacity = '1';
                }

                // Bölümün tamamını görünür yap
                deviceFunctions.style.opacity = '1';
            }

            if (appFunctions) {
                toggleSectionDisabled(appFunctions, true);
            }

            // Bağlantı bildirimi daha önce gösterilmediyse göster
            if (!connectionNotificationShown) {
                showNotification('iOS cihaz bağlandı. Desteklenen özellikler etkinleştirildi.', 'success');
                connectionNotificationShown = true;
            }
        }
    }
}

// Bir bölümü etkinleştir/devre dışı bırak
function toggleSectionDisabled(section, disabled) {
    // Bölümün opasite değerini ayarla
    section.style.opacity = disabled ? '0.6' : '1';

    // Bölüm içindeki tüm butonları ve input'ları bul
    const buttons = section.querySelectorAll('button');
    const inputs = section.querySelectorAll('input, textarea');

    // Butonları etkinleştir/devre dışı bırak
    buttons.forEach(button => {
        button.disabled = disabled;
        if (disabled) {
            button.classList.add('cursor-not-allowed');
        } else {
            button.classList.remove('cursor-not-allowed');
        }
    });

    // Input'ları etkinleştir/devre dışı bırak
    inputs.forEach(input => {
        input.disabled = disabled;
        if (disabled) {
            input.classList.add('cursor-not-allowed');
        } else {
            input.classList.remove('cursor-not-allowed');
        }
    });
}

// Video-SS tablosu için sıralama değişkenleri
let videoSsSort = { column: 'date', direction: 'desc' };

// Video-SS tablosu için çoklu seçim değişkenleri
let videoSsIsSelecting = false;
let videoSsStartRowIndex = -1;
let videoSsLastSelectedIndex = -1;
let videoSsData = []; // Backend'den gelen veri

// Video-SS tablosu için context menu kurulumu
function setupVideoSsContextMenu() {
    const tableBody = document.getElementById('videoSsTableBody');
    const contextMenu = document.getElementById('videoSsContextMenu');

    if (!tableBody || !contextMenu) return;

    // Çoklu seçim için mouse down eventi
    tableBody.addEventListener('mousedown', (e) => {
        // Sağ tık için kontrol ekleyelim
        if (e.button === 2) { // 2 = sağ tık
            return; // Sağ tıkta seçim işlemini atlayalim
        }

        const row = e.target.closest('tr');
        if (!row || !row.hasAttribute('data-index')) return;

        videoSsIsSelecting = true;
        videoSsStartRowIndex = parseInt(row.dataset.index);
        videoSsLastSelectedIndex = videoSsStartRowIndex;

        // Ctrl tuşuna basılı değilse önceki seçimleri temizle
        if (!e.ctrlKey) {
            clearVideoSsSelection();
        }

        toggleVideoSsRowSelection(row);

        // Text seçimini engelle
        e.preventDefault();
    });

    // Mouse move eventi
    tableBody.addEventListener('mousemove', (e) => {
        if (!videoSsIsSelecting) return;

        const row = e.target.closest('tr');
        if (!row || !row.hasAttribute('data-index')) return;

        const currentIndex = parseInt(row.dataset.index);
        if (currentIndex === videoSsLastSelectedIndex) return;

        // Seçim aralığını güncelle
        const rows = tableBody.querySelectorAll('tr[data-index]');
        const start = Math.min(videoSsStartRowIndex, currentIndex);
        const end = Math.max(videoSsStartRowIndex, currentIndex);

        rows.forEach((row) => {
            const rowIndex = parseInt(row.dataset.index);
            if (rowIndex >= start && rowIndex <= end) {
                selectVideoSsRow(row);
            } else if (!e.ctrlKey) {
                deselectVideoSsRow(row);
            }
        });

        videoSsLastSelectedIndex = currentIndex;
    });

    // Mouse up eventi
    document.addEventListener('mouseup', () => {
        videoSsIsSelecting = false;
    });

    // Sağ tık eventi
    tableBody.addEventListener('contextmenu', (e) => {
        e.preventDefault();

        const clickedRow = e.target.closest('tr');
        if (!clickedRow || !clickedRow.hasAttribute('data-index')) return;

        // Eğer tıklanan satır seçili değilse ve Ctrl tuşuna basılı değilse
        if (!clickedRow.classList.contains('selected') && !e.ctrlKey) {
            clearVideoSsSelection();
            selectVideoSsRow(clickedRow);
        }

        // Seçili satır kontrolü
        const selectedRows = getSelectedVideoSsRows();
        if (selectedRows.length === 0) return;

        // Context menu'yü konumlandır - ekranın dışına taşmasını önle
        positionContextMenu(contextMenu, e.clientX, e.clientY);
        contextMenu.classList.remove('hidden');
    });

    // Sayfa herhangi bir yerine tıklandığında context menu'yü gizle
    document.addEventListener('click', (e) => {
        if (!contextMenu.contains(e.target)) {
            contextMenu.classList.add('hidden');
        }
    });

    // "Aç" liste öğesine tıklanırsa
    const openItem = document.querySelector('#contextOpen');
    if (openItem) {
        openItem.addEventListener('click', () => {
            const selectedRows = getSelectedVideoSsRows();
            if (selectedRows.length > 0) {
                // İlk seçili dosyayı aç
                postMessage({
                    type: "openFile",
                    filename: selectedRows[0].data.DosyaAdi
                });
            }
            contextMenu.classList.add('hidden');
        });
    }

    // "Kopyala" liste öğesine tıklanırsa
    const copyItem = document.querySelector('#contextCopy');
    if (copyItem) {
        copyItem.addEventListener('click', () => {
            const selectedRows = getSelectedVideoSsRows();
            if (selectedRows.length > 0) {
                // Tüm seçili dosyaları kopyala
                const filenames = selectedRows.map(row => row.data.DosyaAdi);
                postMessage({
                    type: "copyFiles",
                    filenames: filenames
                });
            }
            contextMenu.classList.add('hidden');
        });
    }

    // "Sil" liste öğesine tıklanırsa
    const deleteItem = document.querySelector('#contextDelete');
    if (deleteItem) {
        deleteItem.addEventListener('click', () => {
            const selectedRows = getSelectedVideoSsRows();
            if (selectedRows.length > 0) {
                const message = selectedRows.length === 1
                    ? 'Dosyayı silmek istediğinizden emin misiniz?'
                    : `${selectedRows.length} dosyayı silmek istediğinizden emin misiniz?`;

                showConfirmationModal(message, () => {
                    const filenames = selectedRows.map(row => row.data.DosyaAdi);
                    postMessage({
                        type: "deleteFiles",
                        filenames: filenames
                    });
                });
            }
            contextMenu.classList.add('hidden');
        });
    }

    // "Drive'a Yükle" liste öğesine tıklanırsa
    const uploadToDriveItem = document.querySelector('#contextUploadToDrive');
    if (uploadToDriveItem) {
        uploadToDriveItem.addEventListener('click', () => {
            const selectedRows = getSelectedVideoSsRows();
            if (selectedRows.length > 0) {
                // Seçili dosyaları Drive'a yükle
                const filenames = selectedRows.map(row => row.data.DosyaAdi);
                // Drive klasör seçim modalini aç
                openDriveFolderModal(filenames);
            }
            contextMenu.classList.add('hidden');
        });
    }
}

// Video-SS tablosu için seçim fonksiyonları
function clearVideoSsSelection() {
    const rows = document.querySelectorAll('#videoSsTableBody tr[data-index]');
    rows.forEach(row => deselectVideoSsRow(row));
}

function toggleVideoSsRowSelection(row) {
    if (row.classList.contains('selected')) {
        deselectVideoSsRow(row);
    } else {
        selectVideoSsRow(row);
    }
}

function selectVideoSsRow(row) {
    row.classList.add('selected');
    row.classList.add('bg-blue-600/50');
}

function deselectVideoSsRow(row) {
    row.classList.remove('selected');
    row.classList.remove('bg-blue-600/50');
}

// Seçili video-ss satırlarını getiren yardımcı fonksiyon
function getSelectedVideoSsRows() {
    const selectedRows = Array.from(document.querySelectorAll('#videoSsTableBody tr.selected'));
    return selectedRows.map(row => {
        const index = parseInt(row.dataset.index);
        return {
            element: row,
            data: videoSsData[index],
            originalIndex: index
        };
    });
}

// Video-SS tablosunu filtreleme fonksiyonu
function filterVideoSsTable() {
    const searchInput = document.getElementById('videoSsSearchInput');
    const tableBody = document.getElementById('videoSsTableBody');

    if (!searchInput || !tableBody) return;

    const filter = searchInput.value.toLowerCase();
    const rows = tableBody.querySelectorAll('tr');
    let visibleCount = 0;

    // Seçili satırların durumunu sakla
    const selectedIndices = new Set();
    document.querySelectorAll('#videoSsTableBody tr.selected').forEach(row => {
        if (row.hasAttribute('data-index')) {
            selectedIndices.add(parseInt(row.getAttribute('data-index')));
        }
    });

    // Önce tüm satırları tarayıp, arama kriterine uyanları gösteriyoruz
    Array.from(rows).forEach(row => {
        // Eğer satırda daha önceden "no-result-row" sınıfı eklenmişse, onu atla
        if (row.classList.contains('no-result-row')) {
            row.remove();
            return;
        }

        const cells = row.querySelectorAll('td');
        let rowContainsFilter = false;

        cells.forEach(cell => {
            if (cell.textContent.toLowerCase().includes(filter)) {
                rowContainsFilter = true;
            }
        });

        if (rowContainsFilter) {
            row.style.display = '';
            visibleCount++;

            // Seçim durumunu geri yükle
            if (row.hasAttribute('data-index')) {
                const rowIndex = parseInt(row.getAttribute('data-index'));
                if (selectedIndices.has(rowIndex)) {
                    selectVideoSsRow(row);
                }
            }
        } else {
            row.style.display = 'none';
        }
    });

    // Eğer görünen satır yoksa "Sonuç bulunamadı" mesajı göster
    if (visibleCount === 0) {
        const noResultRow = document.createElement('tr');
        noResultRow.classList.add('no-result-row');

        const cell = document.createElement('td');
        cell.colSpan = 5;
        cell.classList.add('text-center', 'px-4', 'py-3', 'text-gray-400');
        cell.textContent = 'Arama kriterlerine uygun veri bulunamadı';

        noResultRow.appendChild(cell);
        tableBody.appendChild(noResultRow);
    }
}

// Video-SS tablosunu sıralama fonksiyonu
function sortVideoSsTable(column) {
    const table = document.getElementById('videoSsTable');
    const headers = table.querySelectorAll('th');
    const currentHeader = table.querySelector(`th[data-sort="${column}"]`);
    const tbody = document.getElementById('videoSsTableBody');

    // Sıralama yönünü belirle
    if (videoSsSort.column === column) {
        videoSsSort.direction = videoSsSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        videoSsSort.column = column;
        videoSsSort.direction = 'asc';
    }

    // Tüm sort ikonlarını resetle
    headers.forEach(header => {
        const icon = header.querySelector('i');
        icon.className = 'fas fa-sort text-gray-500 ml-1';
    });

    // Aktif sütunun ikonunu güncelle
    const icon = currentHeader.querySelector('i');
    icon.className = `fas fa-sort-${videoSsSort.direction === 'asc' ? 'up' : 'down'} text-custom ml-1`;

    // Seçili satırların durumunu sakla
    const selectedIndices = new Set();
    document.querySelectorAll('#videoSsTableBody tr.selected').forEach(row => {
        if (row.hasAttribute('data-index')) {
            selectedIndices.add(parseInt(row.getAttribute('data-index')));
        }
    });

    // Tablodaki satırları sırala
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // No-result satırını filtrele
    const dataRows = rows.filter(row => !row.classList.contains('no-result-row'));

    // Sıralama indeksini belirle (HTML'deki sütun sırasına göre)
    let columnIndex = 0;
    switch(column) {
        case 'fileName': columnIndex = 0; break; // Dosya Adı
        case 'gameName': columnIndex = 1; break; // Oyun Adı
        case 'date': columnIndex = 2; break;     // Tarih
        case 'type': columnIndex = 3; break;      // Tip
        case 'status': columnIndex = 4; break;    // Durum
        default: columnIndex = 0;
    }

    // Satırları sırala
    dataRows.sort((a, b) => {
        // Önce orijinal veri indekslerini al
        const aIndex = parseInt(a.getAttribute('data-index'));
        const bIndex = parseInt(b.getAttribute('data-index'));

        // Status sütunu için özel işlem
        if (column === 'status') {
            // Status değerlerini al
            const aStatus = videoSsData[aIndex]?.Status || '';
            const bStatus = videoSsData[bIndex]?.Status || '';

            // Boş status değerlerini en sona koy
            if (!aStatus && bStatus) return videoSsSort.direction === 'asc' ? 1 : -1;
            if (aStatus && !bStatus) return videoSsSort.direction === 'asc' ? -1 : 1;

            // İki status da doluysa normal karşılaştır
            if (aStatus && bStatus) {
                return videoSsSort.direction === 'asc' ?
                    aStatus.localeCompare(bStatus) :
                    bStatus.localeCompare(aStatus);
            }

            // İki status da boşsa, dosya adına göre sırala
            return videoSsSort.direction === 'asc' ?
                videoSsData[aIndex].DosyaAdi.localeCompare(videoSsData[bIndex].DosyaAdi) :
                videoSsData[bIndex].DosyaAdi.localeCompare(videoSsData[aIndex].DosyaAdi);
        }

        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        // Sadece 'date' sütunu için tarih sıralaması yap
        if (column === 'date') {
            // data-date özniteliğinden tarih değerlerini al
            const aDateAttr = a.cells[columnIndex].getAttribute('data-date');
            const bDateAttr = b.cells[columnIndex].getAttribute('data-date');

            // Tarih değerlerini Date nesnelerine çevir
            const dateA = parseDateValue(aDateAttr || aValue);
            const dateB = parseDateValue(bDateAttr || bValue);

            // Tarih karşılaştırması yap
            return videoSsSort.direction === 'asc' ? dateA - dateB : dateB - dateA;
        }

        // Sayısal veriler için karşılaştırma
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return videoSsSort.direction === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // Metin karşılaştırması
        const aLower = aValue.toLowerCase();
        const bLower = bValue.toLowerCase();

        return videoSsSort.direction === 'asc' ?
            aLower.localeCompare(bLower) :
            bLower.localeCompare(aLower);
    });

    // Tabloyu temizle
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    // Sıralanmış satırları ekle ve seçim durumunu geri yükle
    dataRows.forEach(row => {
        // Seçim durumunu kontrol et ve geri yükle
        if (row.hasAttribute('data-index')) {
            const rowIndex = parseInt(row.getAttribute('data-index'));
            if (selectedIndices.has(rowIndex)) {
                row.classList.add('selected');
                row.classList.add('bg-blue-600/50');
            }
        }
        tbody.appendChild(row);
    });
}

// Yerel Log Dosyaları İçin Fonksiyonlar

// Yerel log dosyaları için global değişkenler
let localLogsData = [];
let localLogsSort = { column: 'date', direction: 'desc' };
let currentLocalLogFilename = null;
let selectedLocalLogFiles = new Set(); // Seçili dosyaları saklamak için

// Yerel log dosyaları için çoklu seçim değişkenleri
let localLogsIsSelecting = false;
let localLogsStartRowIndex = -1;
let localLogsLastSelectedIndex = -1;

// Yerel log dosyalarını yükle
function loadLocalLogFiles() {
    // Yükleniyor göstergesi ekle
    const tableBody = document.getElementById('localLogsTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="4" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Yükleniyor...</span>
                </div>
            </td>
        </tr>
    `;

    // C# tarafına istek gönder
    postMessage({
        type: 'getAllLocalLogFiles'
    });
}

// Yerel log dosyalarını göster
function displayLocalLogFiles(data) {
    localLogsData = data;
    const tableBody = document.getElementById('localLogsTableBody');
    tableBody.innerHTML = '';

    if (data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-4 text-gray-400">
                    Herhangi bir log dosyası bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Arama filtresi varsa uygula
    const searchInput = document.getElementById('localLogsSearchInput');
    let filteredData = data;

    if (searchInput && searchInput.value.trim() !== '') {
        const searchTerm = searchInput.value.trim().toLowerCase();
        filteredData = data.filter(file =>
            file.FileName.toLowerCase().includes(searchTerm)
        );

        if (filteredData.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center py-4 text-gray-400">
                        Arama kriterine uygun dosya bulunamadı
                    </td>
                </tr>
            `;
            return;
        }
    }

    // Verileri sırala
    sortLocalLogFiles();

    // Seçim butonlarını güncelle
    updateLocalLogsSelectionButtons();

    // Tabloya verileri ekle
    localLogsData.forEach((file, index) => {
        const row = document.createElement('tr');
        row.className = 'border-b border-gray-600 hover:bg-gray-600/50';
        row.setAttribute('data-index', index);
        row.setAttribute('data-filename', file.FileName);
        row.setAttribute('data-fullpath', file.FullPath || '');
        row.setAttribute('data-relativepath', file.RelativePath || '');

        // Seçim hücresi
        const checkboxCell = document.createElement('td');
        checkboxCell.className = 'px-4 py-3';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'form-checkbox h-4 w-4 text-custom rounded border-gray-600 bg-gray-700';
        checkbox.checked = selectedLocalLogFiles.has(file.FileName);
        checkbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                selectLocalLogRow(row);
            } else {
                deselectLocalLogRow(row);
            }
            updateLocalLogsSelectionButtons();
        });
        checkboxCell.appendChild(checkbox);
        //row.appendChild(checkboxCell);

        // Dosya adı hücresi
        const fileNameCell = document.createElement('td');
        fileNameCell.className = 'px-4 py-3';
        fileNameCell.style = 'max-width: 239px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block;';
        const fileLink = document.createElement('a');
        fileLink.href = '#';
        fileLink.className = 'text-custom hover:underline file-link';
        fileLink.setAttribute('data-filename', file.FileName);
        fileLink.textContent = file.FileName;
        fileNameCell.appendChild(fileLink);
        row.appendChild(fileNameCell);

        // Tarih hücresi
        const dateCell = document.createElement('td');
        dateCell.className = 'px-4 py-3';
        dateCell.textContent = file.Date;
        row.appendChild(dateCell);

        // Boyut hücresi
        const sizeCell = document.createElement('td');
        sizeCell.className = 'px-4 py-3';
        sizeCell.textContent = formatFileSize(file.Size);
        row.appendChild(sizeCell);

        // İşlemler hücresi
        const actionsCell = document.createElement('td');
        actionsCell.className = 'px-4 py-3 text-right';

        const openButton = document.createElement('button');
        openButton.className = 'text-gray-300 hover:text-custom mr-2';
        openButton.innerHTML = '<i class="fas fa-folder-open"></i>';
        openButton.title = 'Aç';
        openButton.onclick = () => openLocalLogFile(file.FileName);

        const openLocationButton = document.createElement('button');
        openLocationButton.className = 'text-gray-300 hover:text-custom mr-2';
        openLocationButton.innerHTML = '<i class="fas fa-external-link-alt"></i>';
        openLocationButton.title = 'Dosya Konumunu Aç';
        openLocationButton.onclick = () => openLocalLogFileLocation(file.FileName);

        const uploadButton = document.createElement('button');
        uploadButton.className = 'text-gray-300 hover:text-custom mr-2';
        uploadButton.innerHTML = '<i class="fas fa-upload"></i>';
        uploadButton.title = 'FTP\'ye Yükle';
        uploadButton.onclick = () => uploadLogFileToFtp(file.FileName);

        const deleteButton = document.createElement('button');
        deleteButton.className = 'text-gray-300 hover:text-red-500';
        deleteButton.innerHTML = '<i class="fas fa-trash-alt"></i>';
        deleteButton.title = 'Sil';
        deleteButton.onclick = () => deleteLocalLogFile(file.FileName);

        actionsCell.appendChild(openButton);
        actionsCell.appendChild(openLocationButton);
        actionsCell.appendChild(uploadButton);
        actionsCell.appendChild(deleteButton);
        row.appendChild(actionsCell);

        // Satıra sağ tık olayı ekle
        row.addEventListener('contextmenu', showLocalLogsContextMenu);

        // Seçili ise sınıf ekle
        if (selectedLocalLogFiles.has(file.FileName)) {
            row.classList.add('bg-blue-600/20');
        }

        tableBody.appendChild(row);
    });
}

// Yerel log dosyalarını filtrele
function filterLocalLogFiles() {
    const searchInput = document.getElementById('localLogsSearchInput');
    if (!searchInput) return;

    // Filtreleme yap ve göster
    displayLocalLogFiles(localLogsData);
}

// Yerel log dosyalarını sırala
function sortLocalLogFiles() {
    localLogsData.sort((a, b) => {
        let aValue, bValue;

        switch (localLogsSort.column) {
            case 'fileName':
                aValue = a.FileName.toLowerCase();
                bValue = b.FileName.toLowerCase();
                break;
            case 'date':
                // Tarih değerlerini Date nesnelerine çevir
                aValue = parseDateValue(a.Date);
                bValue = parseDateValue(b.Date);
                break;
            case 'size':
                // Boyut değerlerini sayıya çevir
                aValue = parseFloat(a.Size) || 0;
                bValue = parseFloat(b.Size) || 0;
                break;
            default:
                // Varsayılan olarak tarihe göre sırala
                aValue = parseDateValue(a.Date);
                bValue = parseDateValue(b.Date);
        }

        // Sayısal karşılaştırma için
        if (aValue instanceof Date && bValue instanceof Date) {
            return localLogsSort.direction === 'asc' ? aValue - bValue : bValue - aValue;
        }
        // Diğer tüm değerler için
        else if (localLogsSort.direction === 'asc') {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        } else {
            return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
        }
    });
}

// Dosya boyutunu formatla
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Yerel log dosyasını aç
function openLocalLogFile(filename) {
    postMessage({
        type: 'openLocalLogFile',
        filename: filename
    });
}

// Yerel log dosyasını sil
function deleteLocalLogFile(filename) {
    showConfirmationModal(`"${filename}" dosyasını silmek istediğinizden emin misiniz?`, () => {
        postMessage({
            type: 'deleteLocalLogFile',
            filename: filename
        });
    });
}

// FTP'ye tekrar yükle
function uploadLogFileToFtp(filename) {
    postMessage({
        type: 'uploadLogFileToFtp',
        filename: filename
    });
}

// Dosya konumunu aç
function openLocalLogFileLocation(filename) {
    postMessage({
        type: 'openLocalLogFileLocation',
        filename: filename
    });
}

// Context menü göster
function showLocalLogsContextMenu(e) {
    e.preventDefault();

    const clickedRow = e.target.closest('tr');
    if (!clickedRow || !clickedRow.hasAttribute('data-index')) return;

    // Eğer tıklanan satır seçili değilse ve Ctrl tuşuna basılı değilse
    if (!clickedRow.classList.contains('selected') && !e.ctrlKey) {
        deselectAllLocalLogs();
        selectLocalLogRow(clickedRow);
    }

    // Seçili satır kontrolü
    const selectedRows = getSelectedLocalLogRows();
    if (selectedRows.length === 0) return;

    // Tek dosya seçilmişse, o dosyanın adını sakla
    if (selectedRows.length === 1) {
        currentLocalLogFilename = selectedRows[0].filename;
    } else {
        currentLocalLogFilename = null; // Birden fazla dosya seçilmişse null olarak işaretle
    }

    const contextMenu = document.getElementById('localLogsContextMenu');
    // Context menu'yü konumlandır - ekranın dışına taşmasını önle
    positionContextMenu(contextMenu, e.clientX, e.clientY);
    contextMenu.classList.remove('hidden');
}

// Seçim butonlarını güncelle
function updateLocalLogsSelectionButtons() {
    const uploadSelectedBtn = document.getElementById('uploadSelectedToFtp');
    const deleteSelectedBtn = document.getElementById('deleteSelectedLogs');
    const headerCheckbox = document.getElementById('headerCheckbox');

    if (uploadSelectedBtn && deleteSelectedBtn) {
        const hasSelection = selectedLocalLogFiles.size > 0;
        uploadSelectedBtn.disabled = !hasSelection;
        deleteSelectedBtn.disabled = !hasSelection;

        // Butonların stilini güncelle
        if (hasSelection) {
            uploadSelectedBtn.classList.remove('bg-gray-700');
            uploadSelectedBtn.classList.add('bg-custom');
            deleteSelectedBtn.classList.remove('bg-gray-700');
            deleteSelectedBtn.classList.add('bg-red-500/20');
        } else {
            uploadSelectedBtn.classList.add('bg-gray-700');
            uploadSelectedBtn.classList.remove('bg-custom');
            deleteSelectedBtn.classList.add('bg-gray-700');
            deleteSelectedBtn.classList.remove('bg-red-500/20');
        }
    }

    // Başlık checkbox'unu güncelle
    if (headerCheckbox) {
        const allSelected = selectedLocalLogFiles.size === localLogsData.length && localLogsData.length > 0;
        const someSelected = selectedLocalLogFiles.size > 0 && selectedLocalLogFiles.size < localLogsData.length;

        headerCheckbox.checked = allSelected;
        headerCheckbox.indeterminate = someSelected;
    }
}

// Tümünü seç
function selectAllLocalLogs() {
    localLogsData.forEach(file => {
        selectedLocalLogFiles.add(file.FileName);
    });

    // Tablodaki tüm satırları güncelle
    const rows = document.querySelectorAll('#localLogsTableBody tr');
    rows.forEach(row => {
        selectLocalLogRow(row);
    });

    updateLocalLogsSelectionButtons();
}

// Seçimi kaldır
function deselectAllLocalLogs() {
    selectedLocalLogFiles.clear();

    // Tablodaki tüm satırları güncelle
    const rows = document.querySelectorAll('#localLogsTableBody tr');
    rows.forEach(row => {
        deselectLocalLogRow(row);
    });

    updateLocalLogsSelectionButtons();
}

// Satırı seç
function selectLocalLogRow(row) {
    if (!row) return;

    row.classList.add('selected');
    row.classList.add('bg-blue-600/20');

    // Checkbox'u işaretle
    const checkbox = row.querySelector('input[type="checkbox"]');
    if (checkbox) checkbox.checked = true;

    // Seçili dosyalar listesine ekle
    const filename = row.getAttribute('data-filename');
    if (filename) {
        selectedLocalLogFiles.add(filename);
    }
}

// Satırın seçimini kaldır
function deselectLocalLogRow(row) {
    if (!row) return;

    row.classList.remove('selected');
    row.classList.remove('bg-blue-600/20');

    // Checkbox'un işaretini kaldır
    const checkbox = row.querySelector('input[type="checkbox"]');
    if (checkbox) checkbox.checked = false;

    // Seçili dosyalar listesinden çıkar
    const filename = row.getAttribute('data-filename');
    if (filename) {
        selectedLocalLogFiles.delete(filename);
    }
}

// Satırın seçim durumunu değiştir
function toggleLocalLogRowSelection(row) {
    if (!row) return;

    if (row.classList.contains('selected')) {
        deselectLocalLogRow(row);
    } else {
        selectLocalLogRow(row);
    }
}

// Seçili yerel log satırlarını getiren yardımcı fonksiyon
function getSelectedLocalLogRows() {
    const selectedRows = Array.from(document.querySelectorAll('#localLogsTableBody tr.selected'));
    return selectedRows.map(row => {
        const index = parseInt(row.dataset.index);
        return {
            element: row,
            data: localLogsData[index],
            originalIndex: index,
            filename: row.getAttribute('data-filename')
        };
    });
}

// Seçili dosyaları FTP'ye yükle
function uploadSelectedLogsToFtp() {
    if (selectedLocalLogFiles.size === 0) return;

    const filenames = Array.from(selectedLocalLogFiles);
    postMessage({
        type: 'uploadMultipleLogsToFtp',
        filenames: filenames
    });
}

// Seçili dosyaları sil
function deleteSelectedLogs() {
    if (selectedLocalLogFiles.size === 0) return;

    const filenames = Array.from(selectedLocalLogFiles);
    const message = filenames.length === 1
        ? `"${filenames[0]}" dosyasını silmek istediğinizden emin misiniz?`
        : `${filenames.length} dosyayı silmek istediğinizden emin misiniz?`;

    showConfirmationModal(message, () => {
        postMessage({
            type: 'deleteMultipleLocalLogFiles',
            filenames: filenames
        });
    });
}

// Google Drive Klasör Seçimi İçin Fonksiyonlar

// Seçilen klasör ID'si ve yüklenecek dosyalar
let selectedDriveFolderId = null;
let filesToUpload = [];

// Varsayılan klasör adını oluştur
function generateDefaultFolderName(filenames) {
    if (!filenames || filenames.length === 0) return '';

    // İlk dosyayı örnek olarak al
    const filename = filenames[0];

    // VideoSsData dizisinde dosya adına göre veri bul
    const fileData = videoSsData.find(item => item.DosyaAdi === filename);
    if (!fileData) return '';

    // Oyun adı, OS ve dosya adından versiyon bilgisini çıkar
    const gameName = fileData.OyunAdi || '';
    const fileType = fileData.Tip || '';

    // Dosya adından versiyon bilgisini çıkarmaya çalış
    let version = '';

    // Dosya adında versiyon bilgisi varsa çıkar
    // Örnek: 2023-04-15-10-08-13-AOS-OyunAdi-Testci-1.2.3_45.txt
    const parts = filename.split('-');
    if (parts.length >= 10) {
        // Son parça genellikle versiyon_buildnumber.txt formatında
        const versionPart = parts[9].replace('.txt', '');
        if (versionPart.includes('_')) {
            version = versionPart.split('_')[0];
        } else {
            version = versionPart;
        }
    }

    // Klasör yapısını oluştur: /Oyun Adı/OS/Versiyon
    return `${gameName}/${fileType}/${version}`;
}

// Drive klasör seçim modalini aç
function openDriveFolderModal(filenames) {
    // Yüklenecek dosyaları sakla
    filesToUpload = filenames;

    // Modal'i göster
    const modal = document.getElementById('driveFolderModal');
    modal.classList.remove('hidden');

    // Seç butonunu devre dışı bırak
    document.getElementById('driveSelectBtn').disabled = true;
    selectedDriveFolderId = null;

    // Varsayılan klasör adını oluştur ve input'a yerleştir
    const defaultFolderName = generateDefaultFolderName(filenames);
    document.getElementById('newFolderName').value = defaultFolderName;

    // Klasörleri yükle
    loadDriveFolders('root');

    // Modal kapatma butonuna tıklama olayı ekle
    document.getElementById('driveModalClose').onclick = closeDriveFolderModal;
    document.getElementById('driveCancelBtn').onclick = closeDriveFolderModal;

    // Seç butonuna tıklama olayı ekle
    document.getElementById('driveSelectBtn').onclick = () => {
        if (selectedDriveFolderId) {
            const newFolderName = document.getElementById('newFolderName').value.trim();

            // Eğer yeni klasör adı girilmişse, önce klasörleri oluştur
            if (newFolderName) {
                // Klasör yolunu parçalara ayır (/ ile ayrılmış)
                const folderParts = newFolderName.split('/');

                // Eğer tek bir klasör adı varsa, doğrudan oluştur ve dosyaları yükle
                if (folderParts.length === 1) {
                    // Tek klasör oluşturma işlemi
                    postMessage({
                        type: "createDriveFolder",
                        parentId: selectedDriveFolderId,
                        folderName: newFolderName,
                        uploadAfterCreate: true,
                        filesToUpload: filesToUpload
                    });
                } else {
                    // Alt klasörler için işlem başladığını bildir
                    showNotification('Alt klasörler oluşturuluyor...', 'info');

                    // İç içe klasör oluşturma işlemini başlat
                    postMessage({
                        type: "createNestedFolders",
                        parentId: selectedDriveFolderId,
                        folderPath: newFolderName,
                        filesToUpload: filesToUpload
                    });
                }
            } else {
                // Yeni klasör adı yoksa, doğrudan seçili klasöre yükle
                uploadFilesToDrive(filesToUpload, selectedDriveFolderId);
            }

            closeDriveFolderModal();
        }
    };

    // Yeni klasör oluşturma butonuna tıklama olayı ekle
    document.getElementById('createFolderBtn').onclick = createNewFolder;

    // Arama kutusuna input olayı ekle
    const searchInput = document.getElementById('driveFolderSearch');
    searchInput.value = ''; // Arama kutusunu temizle
    searchInput.addEventListener('input', filterDriveFolders);
}

// Drive klasör seçim modalini kapat
function closeDriveFolderModal() {
    const modal = document.getElementById('driveFolderModal');
    modal.classList.add('hidden');
}

// Drive klasörlerini yükle
function loadDriveFolders(parentId) {
    // Klasör ağacını temizle veya yükleniyor mesajını göster
    if (parentId === 'root') {
        document.getElementById('driveFolderTree').innerHTML = `
            <li class="flex items-center py-1 px-2">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                <span>Klasörler yükleniyor...</span>
            </li>
        `;
    }

    // C# tarafına klasörleri getirmesi için istek gönder
    postMessage({
        type: "getDriveFolders",
        parentId: parentId
    });
}

// Klasör ağacını oluştur
function buildFolderTree(folders, parentId, parentElement) {
    // Eğer root ise, tüm ağacı temizle
    if (parentId === 'root' && !parentElement) {
        const treeElement = document.getElementById('driveFolderTree');
        treeElement.innerHTML = '';

        // Root klasörü ekle
        const rootItem = document.createElement('li');
        rootItem.className = 'folder-item py-1';
        rootItem.innerHTML = `
            <div class="flex items-center px-2 py-1 rounded hover:bg-gray-600 cursor-pointer folder-name" data-folder-id="root">
                <i class="fas fa-folder mr-2 text-yellow-400"></i>
                <span>Drive</span>
            </div>
            <ul class="pl-6 folder-children" data-folder-id="root"></ul>
        `;
        treeElement.appendChild(rootItem);

        // Root klasörünün alt klasörlerini ekle
        const rootChildren = rootItem.querySelector('.folder-children');
        buildFolderTree(folders, 'root', rootChildren);

        // Root klasörüne tıklama olayı ekle
        const rootFolderName = rootItem.querySelector('.folder-name');
        rootFolderName.addEventListener('click', (e) => {
            selectFolder(e.currentTarget);
        });

        return;
    }

    // Alt klasörleri ekle
    folders.forEach(folder => {
        const folderItem = document.createElement('li');
        folderItem.className = 'folder-item py-1';
        folderItem.innerHTML = `
            <div class="flex items-center px-2 py-1 rounded hover:bg-gray-600 cursor-pointer folder-name" data-folder-id="${folder.Id}">
                <i class="fas fa-folder mr-2 text-yellow-400"></i>
                <span>${folder.Name}</span>
            </div>
            <ul class="pl-6 folder-children hidden" data-folder-id="${folder.Id}"></ul>
        `;
        parentElement.appendChild(folderItem);

        // Klasöre tıklama olayı ekle
        const folderName = folderItem.querySelector('.folder-name');
        folderName.addEventListener('click', (e) => {
            selectFolder(e.currentTarget);

            // Alt klasörleri yükle
            const childrenContainer = folderItem.querySelector('.folder-children');
            if (childrenContainer.classList.contains('hidden')) {
                childrenContainer.classList.remove('hidden');

                // Eğer içi boşsa, alt klasörleri yükle
                if (childrenContainer.children.length === 0) {
                    childrenContainer.innerHTML = `
                        <li class="flex items-center py-1 px-2">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            <span>Yükleniyor...</span>
                        </li>
                    `;
                    loadDriveFolders(folder.Id);
                }
            } else {
                childrenContainer.classList.add('hidden');
            }
        });
    });
}

// Klasör seç
function selectFolder(folderElement) {
    // Önceki seçimi kaldır
    document.querySelectorAll('.folder-name.selected').forEach(el => {
        el.classList.remove('selected', 'bg-blue-600');
    });

    // Yeni seçimi işaretle
    if (folderElement) {
        folderElement.classList.add('selected', 'bg-blue-600');

        // Seçilen klasör ID'sini sakla
        selectedDriveFolderId = folderElement.getAttribute('data-folder-id');

        // Klasörü görünür hale getir (parent klasörleri aç)
        makeVisible(folderElement);
    }

    // Seç butonunu etkinleştir
    document.getElementById('driveSelectBtn').disabled = false;
}

// Klasörü görünür hale getir (parent klasörleri aç)
function makeVisible(folderElement) {
    // Parent klasörü bul
    const parentFolderContainer = folderElement.closest('.folder-children');
    if (parentFolderContainer) {
        // Parent klasörü aç
        parentFolderContainer.classList.remove('hidden');

        // Parent klasörün parent'ini bul ve recursive olarak aç
        const parentFolderItem = parentFolderContainer.closest('.folder-item');
        if (parentFolderItem) {
            const parentFolderName = parentFolderItem.querySelector('.folder-name');
            if (parentFolderName) {
                makeVisible(parentFolderName);
            }
        }
    }
}

// Yeni klasör oluştur
function createNewFolder() {
    const folderNameInput = document.getElementById('newFolderName');
    const folderPath = folderNameInput.value.trim();

    if (!folderPath) {
        alert('Lütfen klasör adı girin.');
        return;
    }

    // Seçili bir klasör yoksa uyarı ver
    if (!selectedDriveFolderId) {
        alert('Lütfen önce bir üst klasör seçin.');
        return;
    }

    // Klasör yolunu parçalara ayır (/ ile ayrılmış)
    const folderParts = folderPath.split('/');

    // Eğer tek bir klasör adı varsa, doğrudan oluştur
    if (folderParts.length === 1) {
        createSingleFolder(selectedDriveFolderId, folderPath);
        return;
    }

    // Alt klasörler için işlem başladığını bildir
    showNotification('Alt klasörler oluşturuluyor...', 'info');

    // İç içe klasör oluşturma işlemini başlat
    createNestedFolders(selectedDriveFolderId, folderParts, 0);
}

// Tek bir klasör oluştur
function createSingleFolder(parentId, folderName) {
    // C# tarafına yeni klasör oluşturması için istek gönder
    postMessage({
        type: "createDriveFolder",
        parentId: parentId,
        folderName: folderName
    });
}



// Dosyaları Drive'a yükle
function uploadFilesToDrive(filenames, folderId) {
    postMessage({
        type: "uploadToDrive",
        filenames: filenames,
        folderId: folderId
    });
}

// Drive klasörlerini arama filtresi
function filterDriveFolders() {
    const searchText = document.getElementById('driveFolderSearch').value.toLowerCase().trim();
    const folderItems = document.querySelectorAll('#driveFolderTree .folder-item');

    // Arama metni boşsa tüm klasörleri göster
    if (!searchText) {
        folderItems.forEach(item => {
            item.style.display = '';
        });
        return;
    }

    // Tüm klasörleri gizle
    folderItems.forEach(item => {
        item.style.display = 'none';
    });

    // Arama metnine uyan klasörleri göster
    folderItems.forEach(item => {
        const folderName = item.querySelector('.folder-name span').textContent.toLowerCase();
        if (folderName.includes(searchText)) {
            // Bu klasörü ve tüm üst klasörlerini göster
            showFolderAndParents(item);
        }
    });
}

// Bir klasörü ve tüm üst klasörlerini göster
function showFolderAndParents(folderItem) {
    if (!folderItem) return;

    // Bu klasörü göster
    folderItem.style.display = '';

    // Alt klasörleri aç
    const childrenContainer = folderItem.querySelector('.folder-children');
    if (childrenContainer) {
        childrenContainer.classList.remove('hidden');
    }

    // Üst klasörü bul ve göster
    const parentContainer = folderItem.parentElement;
    if (parentContainer && parentContainer.classList.contains('folder-children')) {
        const parentFolderItem = parentContainer.parentElement;
        showFolderAndParents(parentFolderItem);
    }
}

// File Hasher functionality
let fileHashData = []; // Global variable to store file hash data

// Function to upload APK file
function uploadApk() {
  
    postMessage({ 
      type: 'uploadApk'
    });    
    showNotification('APK yükleme penceresi açılıyor...', 'info');
}

// Function to get file hashes from SQL
function getFileHashes() {
    // Show loading indicator
    const tableBody = document.getElementById('fileHashTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="5" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Veriler yükleniyor...</span>
                </div>
            </td>
        </tr>
    `;

    // Send message to C# to get file hashes
    postMessage({
        type: 'getFileHashes'
    });
}

// Function to check files for changes
function checkFiles() {
    // if (!fileHashData || fileHashData.length === 0) {
    //     showNotification('Önce verileri getirmelisiniz!', 'warning');
    //     return;
    // }

    // Show loading indicator
    const tableBody = document.getElementById('fileHashTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="5" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Dosyalar kontrol ediliyor...</span>
                </div>
            </td>
        </tr>
    `;

    // Send message to C# to check files
    postMessage({
        type: 'checkFiles'
    });
}

// Function to save hash changes
function saveHashes() {
    // Send message to C# to save hash changes
    postMessage({
        type: 'saveHashes'
    });
}

// Function to reset hash table
function resetHashTable() {
    // Show confirmation dialog
    showConfirmationModal('Tüm hash tablosunu sıfırlamak istediğinizden emin misiniz?', () => {
        // Send message to C# to reset hash table
        postMessage({
            type: 'resetHashTable'
        });
    });
}

// Function to update the file hash table with data
function updateFileHashTable(data) {
    // Yeni veri geldiğinde global değişkeni güncelle
    if (data !== fileHashData) {
        fileHashData = data;
    }

    const tableBody = document.getElementById('fileHashTableBody');
    const searchText = document.getElementById('fileHashSearch').value.toLowerCase();
    const showOnlyChanged = document.getElementById('showOnlyChangedFiles').checked;

    // Clear the table
    tableBody.innerHTML = '';

    if (!fileHashData || fileHashData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-4 text-gray-400">
                    Veri bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Filter data based on search text and checkbox
    const filteredData = fileHashData.filter(item => {
        const matchesSearch =
            (item.id && item.id.toString().toLowerCase().includes(searchText)) ||
            (item.filehash && item.filehash.toLowerCase().includes(searchText)) ||
            (item.path && item.path.toLowerCase().includes(searchText)) ||
            (item.filename && item.filename.toLowerCase().includes(searchText));

        const matchesStatus = !showOnlyChanged || (item.status && (item.status.toLowerCase() === 'updated' || item.status.toLowerCase() === 'new'));

        return matchesSearch && matchesStatus;
    });

    // Sıralama uygula
    const sortedData = sortFileHashData(filteredData, currentSortColumn, currentSortDirection);

    // Sıralama ikonlarını güncelle
    updateSortIcons(currentSortColumn);

    // Add rows to the table
    sortedData.forEach(item => {
        const row = document.createElement('tr');
        row.className = 'border-b border-gray-600';

        // Add status class if needed
        if (item.status) {
            if (item.status.toLowerCase() === 'updated') {
                row.classList.add('bg-yellow-800/30');
            } else if (item.status.toLowerCase() === 'new') {
                row.classList.add('bg-green-800/30');
            }
        }

        row.innerHTML = `
            <td class="p-3">${item.id || ''}</td>
            <td class="p-3">${item.filehash || ''}</td>
            <td class="p-3">${item.path || ''}</td>
            <td class="p-3">${item.filename || ''}</td>
            <td class="p-3">${getStatusBadge(item.status || '')}</td>
        `;

        tableBody.appendChild(row);
    });

    // Show no results message if needed
    if (sortedData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-4 text-gray-400">
                    Arama kriterlerine uygun veri bulunamadı
                </td>
            </tr>
        `;
    }

    // Filtrelenmiş verileri C# tarafına gönder
    sendFilteredDataToCSharp(sortedData);
}

// Filtrelenmiş verileri C# tarafına gönderen fonksiyon
function sendFilteredDataToCSharp(filteredData) {
    postMessage({
        type: 'updateHashTable',
        data: filteredData
    });
}

// Helper function to get status badge HTML
function getStatusBadge(status) {
    switch(status.toLowerCase()) {
        case 'updated':
            return `<span class="px-2 py-1 bg-yellow-500/20 text-yellow-500 rounded-full text-sm">Değişti</span>`;
        case 'new':
            return `<span class="px-2 py-1 bg-green-500/20 text-green-500 rounded-full text-sm">Yeni</span>`;
        case 'deleted':
            return `<span class="px-2 py-1 bg-red-500/20 text-red-500 rounded-full text-sm">Silindi</span>`;
        case 'file ok':
        default:
            return `<span class="px-2 py-1 bg-blue-500/20 text-blue-500 rounded-full text-sm">Normal</span>`;
    }
}

// Sıralama için global değişkenler
let currentSortColumn = 'id'; // Varsayılan sıralama kolonu
let currentSortDirection = 'asc'; // Varsayılan sıralama yönü

// Verileri sıralama fonksiyonu
function sortFileHashData(data, column, direction) {
    return [...data].sort((a, b) => {
        let valueA = a[column];
        let valueB = b[column];

        // Sayısal değerler için özel işlem
        if (column === 'id') {
            valueA = parseInt(valueA) || 0;
            valueB = parseInt(valueB) || 0;
        }
        // Sadece tarih sütunları için özel işlem
        else if (column === 'date' || column === 'createdAt' || column === 'updatedAt') {
            // Tarih değerlerini Date nesnelerine çevir
            const dateA = parseDateValue(valueA);
            const dateB = parseDateValue(valueB);

            // Tarih karşılaştırması yap
            if (direction === 'asc') {
                return dateA - dateB;
            } else {
                return dateB - dateA;
            }
        } else {
            // String değerler için null kontrolü
            valueA = valueA || '';
            valueB = valueB || '';

            // String değerleri küçük harfe çevir
            if (typeof valueA === 'string') valueA = valueA.toLowerCase();
            if (typeof valueB === 'string') valueB = valueB.toLowerCase();
        }

        // Sıralama yönüne göre karşılaştır
        if (direction === 'asc') {
            return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
        } else {
            return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
        }
    });
}

// Bir string'in tarih formatında olup olmadığını kontrol eder
function isDateString(str) {
    if (!str || typeof str !== 'string') return false;

    // YYYY-MM-DD formatı
    const isoDateRegex = /^\d{4}-\d{2}-\d{2}(T|\s)?/;
    // DD.MM.YYYY veya DD/MM/YYYY formatı
    const localDateRegex = /^\d{2}[\.|\/]\d{2}[\.|\/]\d{4}/;
    // YYYY-MM-DD HH:MM:SS formatı
    const dateTimeRegex = /^\d{4}-\d{2}-\d{2}(\s|T)\d{2}:\d{2}(:\d{2})?/;

    return isoDateRegex.test(str) || localDateRegex.test(str) || dateTimeRegex.test(str) || !isNaN(Date.parse(str));
}

// Tarih değerini Date nesnesine çevirir
function parseDateValue(value) {
    if (!value) return new Date(0); // Boş değer için en eski tarih

    if (value instanceof Date) return value;

    if (typeof value === 'string') {
        // Video-SS tablosundaki YYYY-MM-DD-HH-MM-SS formatını işle
        const videoSsDateTimeRegex = /^(\d{4})-(\d{2})-(\d{2})-(\d{2})-(\d{2})-(\d{2})$/;
        const videoSsDateTimeMatch = value.match(videoSsDateTimeRegex);
        if (videoSsDateTimeMatch) {
            try {
                const [_, year, month, day, hour, minute, second] = videoSsDateTimeMatch;
                const date = new Date(
                    parseInt(year),
                    parseInt(month) - 1, // JavaScript'te aylar 0-11 arasında
                    parseInt(day),
                    parseInt(hour),
                    parseInt(minute),
                    parseInt(second)
                );

                if (!isNaN(date.getTime())) {
                    return date;
                }
            } catch (e) {
                console.error('YYYY-MM-DD-HH-MM-SS formatı işlenirken hata:', e);
            }
        }

        // Video-SS tablosundaki YYYYMMDD formatını işle
        const videoSsDateRegex = /^(\d{8})$/;
        const videoSsMatch = value.match(videoSsDateRegex);
        if (videoSsMatch) {
            try {
                const dateStr = videoSsMatch[1];
                const year = parseInt(dateStr.substring(0, 4));
                const month = parseInt(dateStr.substring(4, 6)) - 1; // JavaScript'te aylar 0-11 arasında
                const day = parseInt(dateStr.substring(6, 8));
                const date = new Date(year, month, day);
                if (!isNaN(date.getTime())) {
                    return date;
                }
            } catch (e) {
                console.error('YYYYMMDD formatı işlenirken hata:', e);
            }
        }

        // Önce standart Date.parse ile dene
        try {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
                return parsedDate;
            }
        } catch (e) {
            console.error('Standart tarih ayrıştırma hatası:', e);
        }

        // DD.MM.YYYY veya DD/MM/YYYY formatını işle
        try {
            const localDateRegex = /^(\d{2})[\.|\/](\d{2})[\.|\/](\d{4})/;
            const localMatch = value.match(localDateRegex);
            if (localMatch) {
                const [_, day, month, year] = localMatch;
                const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                if (!isNaN(date.getTime())) {
                    return date;
                }
            }
        } catch (e) {
            console.error('Yerel tarih formatı ayrıştırma hatası:', e);
        }

        // Son çare olarak, sayısal bir değer olabilir mi diye kontrol et
        if (/^\d+$/.test(value)) {
            try {
                const numValue = parseInt(value);
                // Eğer 8 haneli bir sayı ise, YYYYMMDD olarak yorumla
                if (value.length === 8) {
                    const year = Math.floor(numValue / 10000);
                    const month = Math.floor((numValue % 10000) / 100) - 1;
                    const day = numValue % 100;

                    const date = new Date(year, month, day);
                    if (!isNaN(date.getTime())) {
                        return date;
                    }
                }
            } catch (e) {
                console.error('Sayısal tarih ayrıştırma hatası:', e);
            }
        }
    }

    // Geçersiz değer için en eski tarih
    
    return new Date(0);
}

// Sıralama ikonlarını güncelleme fonksiyonu
function updateSortIcons(column) {
    // Tüm ikonları sıfırla
    document.querySelectorAll('.sort-icon').forEach(icon => {
        icon.className = 'fas fa-sort ml-2 text-gray-500 sort-icon';
    });

    // Aktif sütunun ikonunu güncelle
    const activeIcon = document.querySelector(`th[data-sort="${column}"] .sort-icon`);
    if (activeIcon) {
        activeIcon.className = `fas fa-sort-${currentSortDirection === 'asc' ? 'up' : 'down'} ml-2 text-custom sort-icon`;
    }
}

// Oyun Verileri için global değişkenler
let gamesData = []; // Tüm oyun verileri
let displayedGamesData = []; // Görüntülenen (filtrelenmiş/sıralanmış) oyun verileri
let gamesDataSort = { column: 'name', direction: 'asc' };
let currentGamesDataItem = null;

// Oyun verilerini getiren fonksiyon
function getGamesData() {
    // Yükleniyor mesajını göster
    const tableBody = document.getElementById('gamesDataTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Yükleniyor...</span>
                </div>
            </td>
        </tr>
    `;

    // C# tarafına istek gönder
    postMessage({
        type: 'getAllGamesData'
    });
}

// Oyun verilerini tabloya ekleyen fonksiyon
function displayGamesData(data) {
    // Filtreleme yapılıyorsa, orijinal veriyi koruyoruz
    if (data !== gamesData) {
        // Filtrelenmiş veri gönderildi
        displayedGamesData = data;
    } else {
        // Tüm veri gönderildi, gamesData'yı güncelle
        gamesData = data;
        // Önce verileri sırala
        displayedGamesData = sortGamesData(data, gamesDataSort.column, gamesDataSort.direction);
    }

    const tableBody = document.getElementById('gamesDataTableBody');
    tableBody.innerHTML = '';

    if (displayedGamesData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4 text-gray-400">
                    Herhangi bir oyun verisi bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Tabloya verileri ekle
    displayedGamesData.forEach((item, index) => {
        const row = document.createElement('tr');
        row.className = 'border-b border-gray-600 hover:bg-gray-600/50';
        row.setAttribute('data-index', index);

        // Durum sınıfı ekle
        if (item.status) {
            if (item.status.toLowerCase() === 'aktif') {
                row.classList.add('bg-green-800/30');
            } else if (item.status.toLowerCase() === 'pasif') {
                row.classList.add('bg-red-800/30');
            } else if (item.status.toLowerCase() === 'pending') {
                row.classList.add('bg-yellow-800/30');
            }
        }

        // Hücreler oluştur - Her zaman görünür sütunlar
        const nameCell = document.createElement('td');
        nameCell.className = 'px-2 py-2 text-xs md:text-sm font-medium';
        nameCell.textContent = item.name || '';
        row.appendChild(nameCell);

        const bundleidCell = document.createElement('td');
        bundleidCell.className = 'px-2 py-2 text-xs md:text-sm';
        bundleidCell.textContent = item.bundleid || '';
        row.appendChild(bundleidCell);

        const publisherCell = document.createElement('td');
        publisherCell.className = 'px-2 py-2 text-xs md:text-sm';
        publisherCell.textContent = item.publisher || '';
        row.appendChild(publisherCell);

        // Durum badge'i oluştur
        let statusBadgeClass = 'bg-gray-500/20 text-gray-500';
        if (item.status) {
            if (item.status.toLowerCase() === 'aktif') {
                statusBadgeClass = 'bg-green-500/20 text-green-500';
            } else if (item.status.toLowerCase() === 'pasif') {
                statusBadgeClass = 'bg-red-500/20 text-red-500';
            } else if (item.status.toLowerCase() === 'pending') {
                statusBadgeClass = 'bg-yellow-500/20 text-yellow-500';
            }
        }

        const statusCell = document.createElement('td');
        statusCell.className = 'px-2 py-2 text-xs md:text-sm';
        statusCell.innerHTML = `<span class="px-2 py-1 rounded-full text-sm ${statusBadgeClass}">${item.status || 'Unknown'}</span>`;
        row.appendChild(statusCell);

        // Küçük ekranlarda gizlenen sütunlar
        const bundleidiosCell = document.createElement('td');
        bundleidiosCell.className = 'px-2 py-2 text-xs md:text-sm hidden md:table-cell';
        bundleidiosCell.textContent = item.bundleidios || '';
        row.appendChild(bundleidiosCell);

        const iosappnameCell = document.createElement('td');
        iosappnameCell.className = 'px-2 py-2 text-xs md:text-sm hidden md:table-cell';
        iosappnameCell.textContent = item.iosappname || '';
        row.appendChild(iosappnameCell);

        const aosinternalCell = document.createElement('td');
        aosinternalCell.className = 'px-2 py-2 text-xs md:text-sm hidden md:table-cell';
        aosinternalCell.textContent = item.aosinternal || '';
        row.appendChild(aosinternalCell);

        const tfurlCell = document.createElement('td');
        tfurlCell.className = 'px-2 py-2 text-xs md:text-sm hidden md:table-cell';
        // TF URL'i kısaltarak göster
        const tfurl = item.tfurl || '';
        tfurlCell.textContent = tfurl.length > 30 ? tfurl.substring(0, 27) + '...' : tfurl;
        if (tfurl) {
            tfurlCell.title = tfurl; // Tam URL'i tooltip olarak göster
        }
        row.appendChild(tfurlCell);



        tableBody.appendChild(row);
    });

    // Bağlam menüsü için event listener'ları ekle
    setupGamesDataContextMenu();
}

// Oyun verilerini sıralama fonksiyonu
function sortGamesData(data, column, direction) {
    return [...data].sort((a, b) => {
        let valueA = a[column];
        let valueB = b[column];

        // Null kontrolü
        valueA = valueA || '';
        valueB = valueB || '';

        // String değerleri küçük harfe çevir
        if (typeof valueA === 'string') valueA = valueA.toLowerCase();
        if (typeof valueB === 'string') valueB = valueB.toLowerCase();

        // Sıralama yönüne göre karşılaştır
        if (direction === 'asc') {
            return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
        } else {
            return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
        }
    });
}

// Oyun verileri tablosunu sıralama fonksiyonu
function sortGamesDataTable(column) {
    // Sıralama yönünü belirle
    if (gamesDataSort.column === column) {
        gamesDataSort.direction = gamesDataSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        gamesDataSort.column = column;
        gamesDataSort.direction = 'asc';
    }

    // Tüm sort ikonlarını resetle
    const headers = document.querySelectorAll('#gamesDataTable th');
    headers.forEach(header => {
        const icon = header.querySelector('i');
        if (icon) icon.className = 'fas fa-sort text-gray-500 ml-1';
    });

    // Aktif sütunun ikonunu güncelle
    const currentHeader = document.querySelector(`#gamesDataTable th[data-sort="${column}"]`);
    if (currentHeader) {
        const icon = currentHeader.querySelector('i');
        if (icon) icon.className = `fas fa-sort-${gamesDataSort.direction === 'asc' ? 'up' : 'down'} text-custom ml-1`;
    }

    // Verileri yeniden göster (filtrelenmiş verileri kullan)
    if (displayedGamesData.length > 0 && displayedGamesData.length < gamesData.length) {
        // Filtrelenmiş veri varsa, onu sırala
        const sortedFilteredData = sortGamesData(displayedGamesData, column, gamesDataSort.direction);
        displayGamesData(sortedFilteredData);
    } else {
        // Filtre yoksa tüm verileri göster
        displayGamesData(gamesData);
    }
}

// Oyun verileri tablosunu filtreleme fonksiyonu
function filterGamesDataTable() {
    const searchInput = document.getElementById('gamesDataSearch');
    const tableBody = document.getElementById('gamesDataTableBody');

    if (!searchInput || !tableBody) return;

    const filter = searchInput.value.toLowerCase();

    if (filter === '') {
        // Filtre boşsa, tüm verileri göster
        displayGamesData(gamesData);
        return;
    }

    // Filtrelenmiş verileri oluştur
    const filteredData = gamesData.filter(item => {
        return (
            (item.name && item.name.toLowerCase().includes(filter)) ||
            (item.bundleid && item.bundleid.toLowerCase().includes(filter)) ||
            (item.bundleidios && item.bundleidios.toLowerCase().includes(filter)) ||
            (item.iosappname && item.iosappname.toLowerCase().includes(filter)) ||
            (item.publisher && item.publisher.toLowerCase().includes(filter))
        );
    });

    // Filtrelenmiş verileri göster
    if (filteredData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4 text-gray-400">
                    Arama kriterlerine uygun veri bulunamadı
                </td>
            </tr>
        `;
        // Boş görüntülenen veri dizisi oluştur
        displayedGamesData = [];
    } else {
        // Sıralanmış ve filtrelenmiş verileri göster
        displayGamesData(filteredData);
    }
}

// Oyun verileri için bağlam menüsü kurulumu
function setupGamesDataContextMenu() {
    const tableBody = document.getElementById('gamesDataTableBody');
    const contextMenu = document.getElementById('gamesDataContextMenu');

    if (!tableBody || !contextMenu) return;

    // Zaten kurulmuş mu kontrol et
    if (tableBody.hasAttribute('data-context-menu-setup')) {
        return;
    }

    // Kurulduğunu işaretle
    tableBody.setAttribute('data-context-menu-setup', 'true');

    // Sağ tık olayı
    tableBody.addEventListener('contextmenu', (e) => {
        e.preventDefault();

        const clickedRow = e.target.closest('tr');
        if (!clickedRow || !clickedRow.hasAttribute('data-index')) return;

        const index = parseInt(clickedRow.getAttribute('data-index'));
        currentGamesDataItem = displayedGamesData[index];

        // Bağlam menüsünü konumlandır
        positionContextMenu(contextMenu, e.clientX, e.clientY);
        contextMenu.classList.remove('hidden');
    });

    // Sayfa herhangi bir yerine tıklandığında bağlam menüsünü gizle
    document.addEventListener('click', (e) => {
        if (!contextMenu.contains(e.target)) {
            contextMenu.classList.add('hidden');
        }
    });

    // "Bundle ID'yi Kopyala" menü öğesine tıklanırsa
    const copyButton = document.getElementById('gamesDataContextCopy');
    copyButton.addEventListener('click', () => {
        if (currentGamesDataItem) {
            // Seçili işletim sistemine göre bundle ID'yi belirle
            const osSelect = document.getElementById('os-select');
            const selectedOS = osSelect ? osSelect.value : 'AOS';

            const bundleId = selectedOS === 'AOS' ?
                currentGamesDataItem.bundleid :
                currentGamesDataItem.bundleidios;

            // Bundle ID'yi panoya kopyala (JavaScript ile)
            copyTextToClipboard(bundleId);

            showNotification('Bundle ID panoya kopyalandı.');
        }
        contextMenu.classList.add('hidden');
    });

    // "Store'da Göster" menü öğesine tıklanırsa
    const openStoreButton = document.getElementById('gamesDataContextOpenStore');
    openStoreButton.addEventListener('click', () => {
        if (currentGamesDataItem) {
            // Seçili işletim sistemine göre store URL'sini belirle
            const osSelect = document.getElementById('os-select');
            const selectedOS = osSelect ? osSelect.value : 'AOS';

            if (selectedOS === 'AOS') {
                postMessage({
                    type: 'viewInStore2',
                    data: {
                        game: currentGamesDataItem.name,
                        os: 'AOS'
                    }
                });
            } else {
                // iOS için App Store URL'sini aç
                if (currentGamesDataItem.tfurl) {
                    postMessage({
                        type: 'openUrl',
                        url: currentGamesDataItem.tfurl
                    });
                } else {
                    showNotification('Bu oyun için App Store URL\'i bulunamadı.', 'error');
                }
            }
        }
        contextMenu.classList.add('hidden');
    });

    // "Düzenle" menü öğesine tıklanırsa
    const editButton = document.getElementById('gamesDataContextEdit');
    editButton.addEventListener('click', () => {
        if (currentGamesDataItem) {
            // Düzenleme işlemi için modal aç
            openGameEditModal(currentGamesDataItem);
        }
        contextMenu.classList.add('hidden');
    });

    // "Sil" menü öğesine tıklanırsa
    const deleteButton = document.getElementById('gamesDataContextDelete');
    deleteButton.addEventListener('click', () => {
        if (currentGamesDataItem) {
            // Silme işlemi için onay iste
            showConfirmationModal(`"${currentGamesDataItem.name}" oyununu silmek istediğinizden emin misiniz?`, () => {
                deleteGame(currentGamesDataItem.name);
            });
        }
        contextMenu.classList.add('hidden');
    });
}

// Oyun düzenleme/ekleme modal fonksiyonları
function openGameEditModal(gameData = null) {
    const modal = document.getElementById('gameEditModal');
    const modalTitle = document.getElementById('gameModalTitle');
    const form = document.getElementById('gameModalForm');

    // Form alanlarını temizle
    form.reset();

    if (gameData) {
        // Düzenleme modu
        modalTitle.textContent = 'Oyun Düzenle';

        // Form alanlarını doldur
        document.getElementById('gameModalName').value = gameData.name || '';
        document.getElementById('gameModalBundleId').value = gameData.bundleid || '';
        document.getElementById('gameModalBundleIdIos').value = gameData.bundleidios || '';
        document.getElementById('gameModalIosAppName').value = gameData.iosappname || '';
        document.getElementById('gameModalAosInternal').value = gameData.aosinternal || '';
        document.getElementById('gameModalTfUrl').value = gameData.tfurl || '';
        document.getElementById('gameModalPublisher').value = gameData.publisher || '';
        document.getElementById('gameModalStatus').value = gameData.status ? gameData.status.toLowerCase() : 'aktif';
    } else {
        // Ekleme modu
        modalTitle.textContent = 'Yeni Oyun Ekle';
    }

    // Modalı göster
    modal.classList.remove('hidden');
}

function closeGameModal() {
    const modal = document.getElementById('gameEditModal');
    modal.classList.add('hidden');
}

function submitGameModal() {
    // Form verilerini al
    const gameData = {
        name: document.getElementById('gameModalName').value,
        bundleid: document.getElementById('gameModalBundleId').value,
        bundleidios: document.getElementById('gameModalBundleIdIos').value,
        iosappname: document.getElementById('gameModalIosAppName').value,
        aosinternal: document.getElementById('gameModalAosInternal').value,
        tfurl: document.getElementById('gameModalTfUrl').value,
        publisher: document.getElementById('gameModalPublisher').value,
        status: document.getElementById('gameModalStatus').value
    };

    // Boş alan kontrolü
    if (!gameData.name || !gameData.bundleid || !gameData.bundleidios || !gameData.iosappname) {
        showNotification('Lütfen gerekli alanları doldurun.', 'error');
        return;
    }

    // Modal başlığına göre ekleme veya düzenleme işlemi yap
    const modalTitle = document.getElementById('gameModalTitle').textContent;

    if (modalTitle === 'Yeni Oyun Ekle') {
        // Yeni oyun ekleme
        postMessage({
            type: 'addGame',
            data: gameData
        });
    } else {
        // Oyun düzenleme
        postMessage({
            type: 'updateGame',
            data: gameData
        });
    }

    // Modalı kapat
    closeGameModal();
}

// Oyun silme fonksiyonu
function deleteGame(gameName) {
    if (!gameName) return;

    postMessage({
        type: 'deleteGame',
        name: gameName
    });
}

// Metni panoya kopyalama fonksiyonu
function copyTextToClipboard(text) {
    if (!text) return;

    // Kopyalama işlemi devam ediyor mu kontrol et
    if (copyTextToClipboard.isProcessing) {
        
        return;
    }

    // İşlemi başlat
    copyTextToClipboard.isProcessing = true;

    // Modern tarayıcılarda Clipboard API kullan
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text)
            .then(() => {
                
                copyTextToClipboard.isProcessing = false;
            })
            .catch(err => {
                console.error('Failed to copy text: ', err);
                // Clipboard API başarısız olursa alternatif yöntem kullan
                fallbackCopyTextToClipboard(text);
                copyTextToClipboard.isProcessing = false;
            });
    } else {
        // Clipboard API desteklenmiyor, alternatif yöntem kullan
        fallbackCopyTextToClipboard(text);
        copyTextToClipboard.isProcessing = false;
    }
}

// İşlem durumu için statik değişken
copyTextToClipboard.isProcessing = false;

// Alternatif kopyalama yöntemi (eski tarayıcılar için)
function fallbackCopyTextToClipboard(text) {
    try {
        // Geçici bir textarea oluştur
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // Textarea'yı görünmez yap ve sayfaya ekle
        textArea.style.position = 'fixed';
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.width = '2em';
        textArea.style.height = '2em';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';
        document.body.appendChild(textArea);

        // Metni seç
        textArea.select();

        try {
            // Modern API ile kopyalamayı dene
            navigator.clipboard.writeText(text).catch(() => {
                // Clipboard API başarısız olursa C# tarafına mesaj gönder
                postMessage({
                    type: 'copyToClipboard',
                    text: text
                });
            });
        } catch (err) {
            // Son çare olarak C# tarafına mesaj gönder
            postMessage({
                type: 'copyToClipboard',
                text: text
            });
        }

        // Geçici textarea'yı kaldır
        document.body.removeChild(textArea);
        
    } catch (err) {
        console.error('Fallback copy method failed: ', err);
        // Son çare olarak C# tarafına mesaj gönder
        postMessage({
            type: 'copyToClipboard',
            text: text
        });
    }
}

// Onay modalı için değişkenler ve fonksiyonlar
let confirmationCallback = null;

function closeConfirmationModal() {
    
    const modal = document.getElementById('confirmationModal');
    if (modal) {
        modal.classList.add('hidden');
        
    } else {
        console.error('Modal element not found');
    }
    confirmationCallback = null;
}

function confirmAction() {
    
    if (typeof confirmationCallback === 'function') {
        
        try {
            confirmationCallback();
        } catch (error) {
            console.error('Error in confirmation callback:', error);
        }
    } else {
        console.warn('No callback function defined');
    }
    closeConfirmationModal();
}


// Kullanıcı Verileri İçin Global Değişkenler
let userData = []; // Tüm kullanıcı verileri
let displayedUserData = []; // Görüntülenen (filtrelenmiş/sıralanmış) kullanıcı verileri
let userDataSort = { column: 'id', direction: 'asc' };
let currentUserDataItem = null;

// Sürüm Verileri İçin Global Değişkenler
let versionData = []; // Tüm sürüm verileri
let displayedVersionData = []; // Görüntülenen (filtrelenmiş/sıralanmış) sürüm verileri
let versionDataSort = { column: 'app', direction: 'asc' };
let currentVersionDataItem = null;

// Studio QA Verileri İçin Global Değişkenler
let studioQAData = []; // Tüm Studio QA verileri
let displayedStudioQAData = []; // Görüntülenen (filtrelenmiş/sıralanmış) Studio QA verileri
let studioQADataSort = { column: 'id', direction: 'asc' };
let currentStudioQADataItem = null;

// Data Viewer Verileri İçin Global Değişkenler
let dataViewerData = []; // Tüm Data Viewer verileri
let displayedDataViewerData = []; // Görüntülenen (filtrelenmiş/sıralanmış) Data Viewer verileri
let dataViewerDataSort = { column: 'fileName', direction: 'asc' };
let currentDataViewerItem = null;
let selectedDataViewerFiles = []; // Seçili dosyaların listesi

// Mouse ile sürükleyerek çoklu seçim için değişkenler
let dataViewerIsMouseDown = false;
let dataViewerStartRowIndex = -1;
let dataViewerLastSelectedRowIndex = -1;

// Kullanıcı verilerini getiren fonksiyon
function getUserData() {
    // Yükleniyor mesajını göster
    const tableBody = document.getElementById('userDataTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="7" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Yükleniyor...</span>
                </div>
            </td>
        </tr>
    `;

    // C# tarafına istek gönder
    postMessage({
        type: 'getAllTestersFromSql'
    });
}

// Kullanıcı verilerini tabloya yükleyen fonksiyon
function loadUserDataToTable(data) {
    userData = data;
    displayedUserData = [...userData];
    renderUserDataTable();
}

// Kullanıcı verilerini tabloya render eden fonksiyon
function renderUserDataTable() {
    const tableBody = document.getElementById('userDataTableBody');
    tableBody.innerHTML = '';

    if (displayedUserData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4 text-gray-400">
                    Kullanıcı verisi bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Verileri sırala
    const sortedData = sortUserData(displayedUserData, userDataSort.column, userDataSort.direction === 'asc');

    // Tabloya ekle
    sortedData.forEach((user, index) => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-700 transition-colors';
        row.setAttribute('data-id', user.id);
        row.setAttribute('data-index', index);

        row.innerHTML = `
            <td class="px-4 py-3">${user.id}</td>
            <td class="px-4 py-3">${user.user}</td>
            <td class="px-4 py-3">${user.role}</td>
            <td class="px-4 py-3">${user.lastlogin || '-'}</td>
            <td class="px-4 py-3">${user.ip || '-'}</td>
            <td class="px-4 py-3 text-right">
                <button class="edit-user-btn text-blue-400 hover:text-blue-300 mr-2" title="Düzenle">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-user-btn text-red-400 hover:text-red-300" title="Sil">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;

        // Düzenle butonuna tıklama olayı ekle
        const editBtn = row.querySelector('.edit-user-btn');
        editBtn.addEventListener('click', () => openUserModal('edit', user));

        // Sil butonuna tıklama olayı ekle
        const deleteBtn = row.querySelector('.delete-user-btn');
        deleteBtn.addEventListener('click', () => openDeleteUserModal(user));

        tableBody.appendChild(row);
    });
}

// Kullanıcı verilerini sıralayan fonksiyon
function sortUserData(data, column, ascending = true) {
    return [...data].sort((a, b) => {
        let aValue = a[column];
        let bValue = b[column];

        // null veya undefined değerleri için
        if (aValue === null || aValue === undefined) aValue = '';
        if (bValue === null || bValue === undefined) bValue = '';

        // Sayısal değerler için
        if (column === 'id') {
            return ascending ? parseInt(aValue) - parseInt(bValue) : parseInt(bValue) - parseInt(aValue);
        }

        // Metin değerleri için
        return ascending ? String(aValue).localeCompare(String(bValue)) : String(bValue).localeCompare(String(aValue));
    });
}

// Kullanıcı verilerini filtreleyen fonksiyon
function filterUserData(searchText) {
    if (!searchText) {
        displayedUserData = [...userData];
    } else {
        searchText = searchText.toLowerCase();
        displayedUserData = userData.filter(user => {
            return (
                (user.user && user.user.toLowerCase().includes(searchText)) ||
                (user.role && user.role.toLowerCase().includes(searchText))
            );
        });
    }
    renderUserDataTable();
}

// Kullanıcı ekleme/düzenleme modalını açan fonksiyon
function openUserModal(mode, user = null) {
    const modal = document.getElementById('userModal');
    const title = document.getElementById('userModalTitle');
    const idInput = document.getElementById('userModalId');
    const usernameInput = document.getElementById('userModalUsername');
    const roleSelect = document.getElementById('userModalRole');

    // Modalı sıfırla
    idInput.value = '';
    usernameInput.value = '';
    roleSelect.value = 'tester';

    if (mode === 'edit' && user) {
        title.textContent = 'Kullanıcı Düzenle';
        idInput.value = user.id;
        usernameInput.value = user.user;
        roleSelect.value = user.role;
        // Şifre alanını boş bırak, değiştirilmek istenirse doldurulacak
    } else {
        title.textContent = 'Kullanıcı Ekle';
    }

    // Modalı göster
    modal.classList.remove('hidden');

    // Kaydet butonuna tıklama olayı ekle
    document.getElementById('userModalSave').onclick = saveUser;

    // İptal butonuna tıklama olayı ekle
    document.getElementById('userModalCancel').onclick = closeUserModal;
}

// Kullanıcı ekleme/düzenleme modalını kapatan fonksiyon
function closeUserModal() {
    const modal = document.getElementById('userModal');
    modal.classList.add('hidden');
}

// Kullanıcı silme onay modalını açan fonksiyon
function openDeleteUserModal(user) {
    const modal = document.getElementById('deleteUserModal');
    const idInput = document.getElementById('deleteUserId');

    idInput.value = user.id;

    // Modalı göster
    modal.classList.remove('hidden');

    // Sil butonuna tıklama olayı ekle
    document.getElementById('deleteUserConfirm').onclick = deleteUser;

    // İptal butonuna tıklama olayı ekle
    document.getElementById('deleteUserCancel').onclick = closeDeleteUserModal;
}

// Kullanıcı silme onay modalını kapatan fonksiyon
function closeDeleteUserModal() {
    const modal = document.getElementById('deleteUserModal');
    modal.classList.add('hidden');
}

// Kullanıcı kaydeden fonksiyon
function saveUser() {
    const idInput = document.getElementById('userModalId');
    const usernameInput = document.getElementById('userModalUsername');
    const roleSelect = document.getElementById('userModalRole');

    const id = idInput.value;
    const username = usernameInput.value.trim();
    const role = roleSelect.value;

    // Validasyon
    if (!username) {
        showNotification('Kullanıcı adı boş olamaz!', 'error');
        return;
    }

    // Yeni kullanıcı ekleme
    if (!id) {

        // C# tarafına yeni kullanıcı ekleme isteği gönder
        postMessage({
            type: 'addUser',
            user: username,
            role: role
        });
    }
    // Mevcut kullanıcıyı güncelleme
    else {
        // C# tarafına kullanıcı güncelleme isteği gönder
        postMessage({
            type: 'updateUser',
            id: id,
            user: username,
            role: role
        });
    }

    // Modalı kapat
    closeUserModal();
}

// Kullanıcı silen fonksiyon
function deleteUser() {
    const idInput = document.getElementById('deleteUserId');
    const id = idInput.value;

    if (!id) return;

    // C# tarafına kullanıcı silme isteği gönder
    postMessage({
        type: 'deleteUser',
        id: id
    });

    // Modalı kapat
    closeDeleteUserModal();
}

// Sürüm verilerini getiren fonksiyon
function getVersionData() {
    // Yükleniyor mesajını göster
    const tableBody = document.getElementById('versionTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="2" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Yükleniyor...</span>
                </div>
            </td>
        </tr>
    `;

    // C# tarafına istek gönder
    postMessage({
        type: 'getVersions'
    });
}

// Sürüm verilerini tabloya yükleyen fonksiyon
function loadVersionDataToTable(data) {
    versionData = data;
    displayedVersionData = [...versionData];
    renderVersionTable();
}

// Sürüm verilerini tabloya render eden fonksiyon
function renderVersionTable() {
    const tableBody = document.getElementById('versionTableBody');
    tableBody.innerHTML = '';

    if (displayedVersionData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="2" class="text-center py-4 text-gray-400">
                    Sürüm verisi bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Verileri sırala
    const sortedData = sortVersionData(displayedVersionData, versionDataSort.column, versionDataSort.direction === 'asc');

    // Tabloya ekle
    sortedData.forEach((item) => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-700 transition-colors';
        row.setAttribute('data-app', item.app);

        row.innerHTML = `
            <td class="px-4 py-3">${item.app}</td>
            <td class="px-4 py-3" contenteditable="true">${item.version}</td>
        `;

        // Hücre değişikliği olayı ekle
        const versionCell = row.querySelector('td[contenteditable="true"]');
        versionCell.addEventListener('blur', () => {
            const newValue = versionCell.textContent.trim();
            if (newValue !== item.version) {
                updateVersion(item.app, newValue);
            }
        });

        tableBody.appendChild(row);
    });
}

// Sürüm verilerini sıralayan fonksiyon
function sortVersionData(data, column, ascending = true) {
    return [...data].sort((a, b) => {
        let aValue = a[column];
        let bValue = b[column];

        // null veya undefined değerleri için
        if (aValue === null || aValue === undefined) aValue = '';
        if (bValue === null || bValue === undefined) bValue = '';

        // Metin değerleri için
        return ascending ? String(aValue).localeCompare(String(bValue)) : String(bValue).localeCompare(String(aValue));
    });
}

// Sürüm verilerini filtreleyen fonksiyon
function filterVersionData(searchText) {
    if (!searchText) {
        displayedVersionData = [...versionData];
    } else {
        searchText = searchText.toLowerCase();
        displayedVersionData = versionData.filter(item => {
            return (
                (item.app && item.app.toLowerCase().includes(searchText))
            );
        });
    }
    renderVersionTable();
}

// Sürüm güncelleyen fonksiyon
function updateVersion(app, newVersion) {
    showConfirmationModal(`${app} uygulamasının sürümünü ${newVersion} olarak güncellemek istediğinizden emin misiniz?`, async () => {
        postMessage({
            type: 'updateVersion',
            app: app,
            version: newVersion
        });

        // Yerel veri güncellemesi
        // Veritabanı güncellemesi C# tarafında yapılıyor ve SendVersionDataToWebView() çağrılıyor
        // Bu nedenle burada manuel güncelleme yapmaya gerek yok
    });
}

// Studio QA verilerini getiren fonksiyon
function getStudioQAData() {
    // Yükleniyor mesajını göster
    const tableBody = document.getElementById('studioQATableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Yükleniyor...</span>
                </div>
            </td>
        </tr>
    `;

    // C# tarafına istek gönder
    postMessage({
        type: 'getAllStudioQAData'
    });
}

// Studio QA verilerini tabloya yükleyen fonksiyon
function loadStudioQADataToTable(data) {
    studioQAData = data;
    displayedStudioQAData = [...studioQAData];
    renderStudioQATable();
}

// Studio QA verilerini tabloya render eden fonksiyon
function renderStudioQATable() {
    const tableBody = document.getElementById('studioQATableBody');
    tableBody.innerHTML = '';

    if (displayedStudioQAData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4 text-gray-400">
                    Studio QA verisi bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Verileri sırala
    const sortedData = sortStudioQAData(displayedStudioQAData, studioQADataSort.column, studioQADataSort.direction === 'asc');

    // Tabloya ekle
    sortedData.forEach((item) => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-700 transition-colors';
        row.setAttribute('data-id', item.id);

        row.innerHTML = `
            <td class="px-4 py-3">${item.id}</td>
            <td class="px-4 py-3">${item.tester}</td>
            <td class="px-4 py-3">${item.priority1}</td>
            <td class="px-4 py-3">${item.priority2}</td>
            <td class="px-4 py-3">${item.priority3}</td>
            <td class="px-4 py-3">${item.priority4}</td>
            <td class="px-4 py-3">${item.priority5}</td>
            <td class="px-4 py-3">${item.priority6}</td>
            <td class="px-4 py-3 text-right">
                <button class="text-blue-500 hover:text-blue-400 mr-2 edit-studioqa-btn" title="Düzenle">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="text-red-500 hover:text-red-400 delete-studioqa-btn" title="Sil">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        // Düzenle butonuna tıklama olayı ekle
        const editBtn = row.querySelector('.edit-studioqa-btn');
        editBtn.addEventListener('click', () => openStudioQAModal('edit', item));

        // Sil butonuna tıklama olayı ekle
        const deleteBtn = row.querySelector('.delete-studioqa-btn');
        deleteBtn.addEventListener('click', () => openDeleteStudioQAModal(item));

        tableBody.appendChild(row);
    });
}

// Studio QA verilerini sıralayan fonksiyon
function sortStudioQAData(data, column, ascending = true) {
    return [...data].sort((a, b) => {
        let aValue = a[column];
        let bValue = b[column];

        // null veya undefined değerleri için
        if (aValue === null || aValue === undefined) aValue = '';
        if (bValue === null || bValue === undefined) bValue = '';

        // ID için sayısal sıralama
        if (column === 'id') {
            return ascending ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue);
        }

        // Metin değerleri için
        return ascending ? String(aValue).localeCompare(String(bValue)) : String(bValue).localeCompare(String(aValue));
    });
}

// Studio QA verilerini filtreleyen fonksiyon
function filterStudioQAData(searchText) {
    if (!searchText) {
        displayedStudioQAData = [...studioQAData];
    } else {
        searchText = searchText.toLowerCase();
        displayedStudioQAData = studioQAData.filter(item => {
            return (
                (item.tester && item.tester.toLowerCase().includes(searchText)) ||
                (item.priority1 && item.priority1.toLowerCase().includes(searchText)) ||
                (item.priority2 && item.priority2.toLowerCase().includes(searchText)) ||
                (item.priority3 && item.priority3.toLowerCase().includes(searchText)) ||
                (item.priority4 && item.priority4.toLowerCase().includes(searchText)) ||
                (item.priority5 && item.priority5.toLowerCase().includes(searchText)) ||
                (item.priority6 && item.priority6.toLowerCase().includes(searchText))
            );
        });
    }
    renderStudioQATable();
}

// Studio QA ekleme/düzenleme modalını açan fonksiyon
function openStudioQAModal(mode, item = null) {
    const modal = document.getElementById('studioQAModal');
    const title = document.getElementById('studioQAModalTitle');
    const idInput = document.getElementById('studioQAModalId');
    const testerInput = document.getElementById('studioQAModalTester');
    const priority1Input = document.getElementById('studioQAModalPriority1');
    const priority2Input = document.getElementById('studioQAModalPriority2');
    const priority3Input = document.getElementById('studioQAModalPriority3');
    const priority4Input = document.getElementById('studioQAModalPriority4');
    const priority5Input = document.getElementById('studioQAModalPriority5');
    const priority6Input = document.getElementById('studioQAModalPriority6');

    // Modalı sıfırla
    idInput.value = '';
    testerInput.value = '';
    priority1Input.value = '';
    priority2Input.value = '';
    priority3Input.value = '';
    priority4Input.value = '';
    priority5Input.value = '';
    priority6Input.value = '';

    if (mode === 'edit' && item) {
        title.textContent = 'Studio QA Kaydını Düzenle';
        idInput.value = item.id;
        testerInput.value = item.tester;
        priority1Input.value = item.priority1;
        priority2Input.value = item.priority2;
        priority3Input.value = item.priority3;
        priority4Input.value = item.priority4;
        priority5Input.value = item.priority5;
        priority6Input.value = item.priority6;
    } else {
        title.textContent = 'Yeni Studio QA Kaydı Ekle';
    }

    // Modalı göster
    modal.classList.remove('hidden');
}

// Studio QA ekleme/düzenleme modalını kapatan fonksiyon
function closeStudioQAModal() {
    const modal = document.getElementById('studioQAModal');
    modal.classList.add('hidden');
}

// Studio QA silme onay modalını açan fonksiyon
function openDeleteStudioQAModal(item) {
    const modal = document.getElementById('deleteStudioQAModal');
    const idInput = document.getElementById('deleteStudioQAId');

    idInput.value = item.id;

    // Modalı göster
    modal.classList.remove('hidden');
}

// Studio QA silme onay modalını kapatan fonksiyon
function closeDeleteStudioQAModal() {
    const modal = document.getElementById('deleteStudioQAModal');
    modal.classList.add('hidden');
}

// Studio QA kaydını kaydeden fonksiyon
function saveStudioQA() {
    const idInput = document.getElementById('studioQAModalId');
    const testerInput = document.getElementById('studioQAModalTester');
    const priority1Input = document.getElementById('studioQAModalPriority1');
    const priority2Input = document.getElementById('studioQAModalPriority2');
    const priority3Input = document.getElementById('studioQAModalPriority3');
    const priority4Input = document.getElementById('studioQAModalPriority4');
    const priority5Input = document.getElementById('studioQAModalPriority5');
    const priority6Input = document.getElementById('studioQAModalPriority6');

    const id = idInput.value;
    const tester = testerInput.value;
    const priority1 = priority1Input.value;
    const priority2 = priority2Input.value;
    const priority3 = priority3Input.value;
    const priority4 = priority4Input.value;
    const priority5 = priority5Input.value;
    const priority6 = priority6Input.value;

    if (!tester) {
        showNotification('Tester alanı boş olamaz!', 'error');
        return;
    }

    // Yeni kayıt ekleme
    if (!id) {
        // C# tarafına yeni kayıt ekleme isteği gönder
        postMessage({
            type: 'addStudioQA',
            tester: tester,
            priority1: priority1,
            priority2: priority2,
            priority3: priority3,
            priority4: priority4,
            priority5: priority5,
            priority6: priority6
        });
    }
    // Mevcut kaydı güncelleme
    else {
        // C# tarafına kayıt güncelleme isteği gönder
        postMessage({
            type: 'updateStudioQA',
            id: id,
            tester: tester,
            priority1: priority1,
            priority2: priority2,
            priority3: priority3,
            priority4: priority4,
            priority5: priority5,
            priority6: priority6
        });
    }

    // Modalı kapat
    closeStudioQAModal();
}

// Studio QA kaydını silen fonksiyon
function deleteStudioQA() {
    const idInput = document.getElementById('deleteStudioQAId');
    const id = idInput.value;

    if (!id) return;

    // C# tarafına kayıt silme isteği gönder
    postMessage({
        type: 'deleteStudioQA',
        id: id
    });

    // Modalı kapat
    closeDeleteStudioQAModal();
}

// Data Viewer dosyalarını getiren fonksiyon
function getDataViewerFiles() {
    // Yükleniyor mesajını göster
    const tableBody = document.getElementById('dataViewerTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="3" class="text-center py-4">
                <div class="flex justify-center items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Yükleniyor...</span>
                </div>
            </td>
        </tr>
    `;

    // C# tarafına istek gönder
    postMessage({
        type: 'getDataViewerFiles'
    });
}

// Data Viewer dosyalarını tabloya yükleyen fonksiyon
function loadDataViewerFilesToTable(data) {
    dataViewerData = data;
    displayedDataViewerData = [...dataViewerData];
    renderDataViewerTable();
}

// Dosya adından bilgileri ayrıştıran fonksiyon
function parseFileName(fileName) {
    try {
        // Örnek: "2025-04-18-16-03-29-IOS-Paper_Ready-ulkem.cifci-version_0_buildNumber_0-status_Genel - 1.49(46).txt"
        const result = {
            testerName: '',
            gameName: '',
            date: '',
            version: '',
            os: '',
            status: '',
            fileName: fileName // Orijinal dosya adını da sakla
        };

        // Dosya adını parçalara ayır
        const parts = fileName.split('-');

        if (parts.length >= 7) {
            // Tarih bilgisini al (ilk 3 parça)
            const year = parts[0];
            const month = parts[1];
            const day = parts[2];
            const hour = parts[3];
            const minute = parts[4];
            const second = parts[5];
            result.date = `${year}-${month}-${day} ${hour}:${minute}:${second}`;

            // OS bilgisini al
            result.os = parts[6];

            // Oyun adını al
            if (parts.length > 7) {
                result.gameName = parts[7].replace(/_/g, ' ');
            }

            // Tester adını al
            if (parts.length > 8) {
                result.testerName = parts[8];
            }

            // Versiyon bilgisini al
            if (parts.length > 9 && parts[9].includes('version_')) {
                const versionPart = parts[9].split('_');
                if (versionPart.length >= 3) {
                    const buildPart = parts[9].split('buildNumber_');
                    if (buildPart.length >= 2) {
                        const version = versionPart[1];
                        const buildNumber = buildPart[1];
                        result.version = `${version} - ${buildNumber}`;
                    }
                }
            }

            // Status bilgisini al
            if (parts.length > 10 && parts[10].includes('status_')) {
                const statusPart = parts[10].split('status_');
                if (statusPart.length >= 2) {
                    // .txt uzantısını kaldır
                    let status = statusPart[1].replace('.txt', '');
                    // Dosya adı içinde başka bilgiler varsa onları da kaldır
                    if (status.includes(' ')) {
                        status = status.split(' ')[0];
                    }
                    result.status = status;
                }
            }
        } else {
            // Yeterli parça yoksa, dosya adını olduğu gibi göster
            result.testerName = 'Bilinmiyor';
            result.gameName = 'Bilinmiyor';
            result.date = 'Bilinmiyor';
            result.version = 'Bilinmiyor';
            result.os = 'Bilinmiyor';
            result.status = 'Bilinmiyor';
        }

        return result;
    } catch (error) {
        console.error('Dosya adı ayrıştırma hatası:', error);
        return {
            testerName: 'Hata',
            gameName: 'Hata',
            date: 'Hata',
            version: 'Hata',
            os: 'Hata',
            status: 'Hata',
            fileName: fileName
        };
    }
}

// Data Viewer dosyalarını tabloya render eden fonksiyon
function renderDataViewerTable() {
    const tableBody = document.getElementById('dataViewerTableBody');
    tableBody.innerHTML = '';

    if (displayedDataViewerData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4 text-gray-400">
                    Dosya bulunamadı
                </td>
            </tr>
        `;
        return;
    }

    // Verileri sırala
    const sortedData = sortDataViewerData(displayedDataViewerData, dataViewerDataSort.column, dataViewerDataSort.direction === 'asc');

    // Tümünü seç checkbox'u durumunu güncelle
    const selectAllCheckbox = document.getElementById('dataViewerSelectAll');
    selectAllCheckbox.checked = selectedDataViewerFiles.length > 0 && selectedDataViewerFiles.length === displayedDataViewerData.length;
    selectAllCheckbox.indeterminate = selectedDataViewerFiles.length > 0 && selectedDataViewerFiles.length < displayedDataViewerData.length;

    // Tabloya ekle
    sortedData.forEach((item) => {
        // Dosya adını ayrıştır
        const parsedData = parseFileName(item.fileName);

        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-700 transition-colors cursor-pointer';
        row.setAttribute('data-filename', item.fileName);

        // Seçili ise sınıf ekle
        if (selectedDataViewerFiles.includes(item.fileName)) {
            row.classList.add('bg-gray-600');
        }

        // Checkbox ve diğer hücreler
        row.innerHTML = `
            <td class="px-4 py-3">
                <input type="checkbox" class="dataViewerFileCheckbox form-checkbox bg-gray-700 border-gray-600 text-custom"
                       ${selectedDataViewerFiles.includes(item.fileName) ? 'checked' : ''} data-filename="${item.fileName}">
            </td>
            <td class="px-4 py-3">${parsedData.testerName}</td>
            <td class="px-4 py-3">${parsedData.gameName}</td>
            <td class="px-4 py-3">${parsedData.date}</td>
            <td class="px-4 py-3">${parsedData.version}</td>
            <td class="px-4 py-3">${parsedData.os}</td>
            <td class="px-4 py-3">${parsedData.status}</td>
            <td class="px-4 py-3">${formatFileSize(item.size)}</td>
        `;

        // Çift tıklama olayı ekle
        row.addEventListener('dblclick', () => {
            downloadAndOpenFile(item.fileName);
        });

        // Sağ tık olayı ekle
        row.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            showDataViewerContextMenu(e, item.fileName);
        });

        // Satıra tıklama olayı ekle (Ctrl tuşu ile çoklu seçim)
        row.addEventListener('click', (e) => {
            // Checkbox'a tıklandıysa işlem yapma (checkbox kendi olayını işleyecek)
            if (e.target.type === 'checkbox') return;

            // Shift tuşu basılıysa aralık seçimi yap
            if (e.shiftKey && dataViewerStartRowIndex !== -1) {
                const currentIndex = Array.from(tableBody.children).indexOf(row);
                selectRowRange(dataViewerStartRowIndex, currentIndex);
            }
            // Ctrl tuşu basılıysa çoklu seçim yap
            else if (e.ctrlKey) {
                toggleFileSelection(item.fileName);
                dataViewerStartRowIndex = Array.from(tableBody.children).indexOf(row);
            } else {
                // Ctrl tuşu basılı değilse, sadece bu dosyayı seç
                selectedDataViewerFiles = [item.fileName];
                dataViewerStartRowIndex = Array.from(tableBody.children).indexOf(row);
                renderDataViewerTable();
            }
        });

        // Mouse down olayı ekle (sürükleyerek seçim için)
        row.addEventListener('mousedown', (e) => {
            // Sol tık ve checkbox dışında bir yere tıklandıysa
            if (e.button === 0 && e.target.type !== 'checkbox') {
                // Metin seçimini engelle
                e.preventDefault();

                dataViewerIsMouseDown = true;
                const rowIndex = Array.from(tableBody.children).indexOf(row);

                // Ctrl tuşu basılı değilse, seçimi temizle
                if (!e.ctrlKey && !e.shiftKey) {
                    selectedDataViewerFiles = [item.fileName];
                    renderDataViewerTable();
                } else if (!selectedDataViewerFiles.includes(item.fileName)) {
                    // Ctrl tuşu basılıysa ve seçili değilse, seçime ekle
                    toggleFileSelection(item.fileName);
                }

                dataViewerStartRowIndex = rowIndex;
                dataViewerLastSelectedRowIndex = rowIndex;
            }
        });

        // Mouse enter olayı ekle (sürükleyerek seçim için)
        row.addEventListener('mouseenter', () => {
            if (dataViewerIsMouseDown) {
                const rowIndex = Array.from(tableBody.children).indexOf(row);

                // Son seçilen satırdan farklıysa
                if (rowIndex !== dataViewerLastSelectedRowIndex) {
                    // Başlangıç noktası ile bu satır arasındaki tüm satırları seç
                    selectRowRange(dataViewerStartRowIndex, rowIndex);
                    dataViewerLastSelectedRowIndex = rowIndex;
                }
            }
        });

        tableBody.appendChild(row);
    });

    // Checkbox'lara olay dinleyicileri ekle
    document.querySelectorAll('.dataViewerFileCheckbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const fileName = this.getAttribute('data-filename');
            toggleFileSelection(fileName);
        });
    });
}

// Data Viewer dosyalarını sıralayan fonksiyon
function sortDataViewerData(data, column, ascending = true) {
    return [...data].sort((a, b) => {
        // Dosya adından ayrıştırılmış alanlar için
        if (['testerName', 'gameName', 'date', 'version', 'os', 'status'].includes(column)) {
            const parsedA = parseFileName(a.fileName);
            const parsedB = parseFileName(b.fileName);

            let aValue = parsedA[column];
            let bValue = parsedB[column];

            // null veya undefined değerleri için
            if (aValue === null || aValue === undefined) aValue = '';
            if (bValue === null || bValue === undefined) bValue = '';

            return ascending ? String(aValue).localeCompare(String(bValue)) : String(bValue).localeCompare(String(aValue));
        } else {
            // Orijinal alanlar için (size, fileName)
            let aValue = a[column];
            let bValue = b[column];

            // null veya undefined değerleri için
            if (aValue === null || aValue === undefined) aValue = '';
            if (bValue === null || bValue === undefined) bValue = '';

            // Boyut için sayısal sıralama
            if (column === 'size') {
                return ascending ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue);
            }

            // Metin değerleri için
            return ascending ? String(aValue).localeCompare(String(bValue)) : String(bValue).localeCompare(String(aValue));
        }
    });
}

// Data Viewer dosyalarını filtreleyen fonksiyon
function filterDataViewerData(searchText) {
    if (!searchText) {
        displayedDataViewerData = [...dataViewerData];
    } else {
        searchText = searchText.toLowerCase();
        displayedDataViewerData = dataViewerData.filter(item => {
            // Dosya adını ayrıştır
            const parsedData = parseFileName(item.fileName);

            // Tüm alanlarda arama yap
            return (
                (parsedData.testerName && parsedData.testerName.toLowerCase().includes(searchText)) ||
                (parsedData.gameName && parsedData.gameName.toLowerCase().includes(searchText)) ||
                (parsedData.date && parsedData.date.toLowerCase().includes(searchText)) ||
                (parsedData.version && parsedData.version.toLowerCase().includes(searchText)) ||
                (parsedData.os && parsedData.os.toLowerCase().includes(searchText)) ||
                (parsedData.status && parsedData.status.toLowerCase().includes(searchText)) ||
                (item.fileName && item.fileName.toLowerCase().includes(searchText))
            );
        });
    }
    renderDataViewerTable();
}

// Dosya boyutunu formatlı gösteren fonksiyon
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Dosyayı indirip açan fonksiyon
function downloadAndOpenFile(fileName) {
    postMessage({
        type: 'downloadAndOpenFile',
        fileName: fileName
    });
}

// Dosya seçimini aç/kapat fonksiyonu
function toggleFileSelection(fileName) {
    const index = selectedDataViewerFiles.indexOf(fileName);
    if (index === -1) {
        // Dosya seçili değilse, seçilenlere ekle
        selectedDataViewerFiles.push(fileName);
    } else {
        // Dosya zaten seçiliyse, seçimden kaldır
        selectedDataViewerFiles.splice(index, 1);
    }
    renderDataViewerTable();
}

// Belirli bir aralıktaki satırları seçen fonksiyon
function selectRowRange(startIndex, endIndex) {
    const tableBody = document.getElementById('dataViewerTableBody');
    const rows = Array.from(tableBody.children);

    // Başlangıç ve bitiş indekslerini sırala
    const start = Math.min(startIndex, endIndex);
    const end = Math.max(startIndex, endIndex);

    // Seçimi temizle ve yeni seçimi oluştur
    selectedDataViewerFiles = [];

    // Aralıktaki tüm satırları seç
    for (let i = start; i <= end; i++) {
        if (i >= 0 && i < rows.length) {
            const fileName = rows[i].getAttribute('data-filename');
            if (fileName && !selectedDataViewerFiles.includes(fileName)) {
                selectedDataViewerFiles.push(fileName);
            }
        }
    }

    renderDataViewerTable();
}

// Tümünü seç/kaldır fonksiyonu
function toggleSelectAllDataViewerFiles() {
    const selectAllCheckbox = document.getElementById('dataViewerSelectAll');

    if (selectAllCheckbox.checked) {
        // Tüm dosyaları seç
        selectedDataViewerFiles = displayedDataViewerData.map(item => item.fileName);
    } else {
        // Tüm seçimleri kaldır
        selectedDataViewerFiles = [];
    }

    renderDataViewerTable();
}

// Sağ tık menüsünü gösteren fonksiyon
function showDataViewerContextMenu(e, fileName) {
    // Eğer dosya seçili değilse, sadece bu dosyayı seç
    if (!selectedDataViewerFiles.includes(fileName)) {
        selectedDataViewerFiles = [fileName];
        renderDataViewerTable();
    }

    const contextMenu = document.getElementById('dataViewerContextMenu');

    // Geliştirilmiş pozisyonlama fonksiyonunu kullan
    positionContextMenu(contextMenu, e.clientX, e.clientY);
    contextMenu.classList.remove('hidden');

    // Menü dışına tıklandığında menüyü kapat
    document.addEventListener('click', closeDataViewerContextMenu);
}

// Sağ tık menüsünü kapatan fonksiyon
function closeDataViewerContextMenu() {
    const contextMenu = document.getElementById('dataViewerContextMenu');
    contextMenu.classList.add('hidden');
    document.removeEventListener('click', closeDataViewerContextMenu);
}

// Seçili dosyaları arşive taşıyan fonksiyon
function archiveSelectedFiles() {
    if (selectedDataViewerFiles.length === 0) {
        showNotification('Lütfen arşive taşınacak dosyaları seçin.', 'warning');
        return;
    }

    // C# tarafına arşivleme isteği gönder
    postMessage({
        type: 'archiveFiles',
        fileNames: selectedDataViewerFiles
    });
}


// iCard Login
let icardToken = null;

function saveIcardLogin(username, token) {
    localStorage.setItem('icardUsername', username);
    localStorage.setItem('icardToken', token);
}
function loadIcardLogin() {
    return {
    username: localStorage.getItem('icardUsername') || '',
    token: localStorage.getItem('icardToken') || null
    };
}
function clearIcardLogin() {
    localStorage.removeItem('icardUsername');
    localStorage.removeItem('icardToken');
}

async function icardLogin() {
    const username = document.getElementById('icardUsername').value.trim();
    const password = document.getElementById('icardPassword').value;
    const apiDetails = document.getElementById('icardApiDetails');
    apiDetails.innerHTML = '<span class="text-gray-400"><i class="fas fa-spinner fa-spin mr-2"></i>Giriş yapılıyor...</span>';

    try {
    const params = new URLSearchParams();
    params.append('grant_type', 'password');
    params.append('username', username);
    params.append('password', password);

    const response = await fetch('https://api.icardportal.com/mobile/30/v1/token', {
        method: 'POST',
        headers: {
        'languagecode': 'tr',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'tr-TR,tr;q=0.9',
        'User-Agent': 'icardv2/258 CFNetwork/3857.100.1 Darwin/25.0.0',
        'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params,
    });

    if (!response.ok) {
        const err = await response.text();
        throw new Error('Giriş başarısız: ' + err);
    }
    const data = await response.json();
    icardToken = data.access_token;
    saveIcardLogin(username, icardToken);
    // C# Settings ile de kaydet
    if (window.external && typeof window.external.SaveIcardLogin === 'function') {
        try { window.external.SaveIcardLogin(username, password); } catch (e) { /* ignore */ }
    }
    apiDetails.innerHTML = `<div class='text-green-400 mb-4'>Giriş başarılı!</div>
    <div class='flex gap-2 mb-4 items-end'>
        <div>
        <label class='block text-xs mb-1'>Başlangıç Tarihi</label>
        <input type='date' id='icardBeginDate' class='bg-gray-700 border border-gray-600 rounded px-2 py-1 text-gray-100' value='${getDefaultBeginDate()}'>
        </div>
        <div>
        <label class='block text-xs mb-1'>Bitiş Tarihi</label>
        <input type='date' id='icardEndDate' class='bg-gray-700 border border-gray-600 rounded px-2 py-1 text-gray-100' value='${getDefaultEndDate()}'>
        </div>
        <button onclick='fetchIcardTransitions()' class='bg-custom hover:bg-custom/90 px-4 py-2 rounded text-white font-semibold transition ml-2'>Geçişleri Listele</button>
    </div>
    <div id='icardTransitionsTableArea'></div>`;
    } catch (e) {
    apiDetails.innerHTML = `<span class='text-red-400'>Hata: ${e.message}</span>`;
    }
}

function getDefaultBeginDate() {
    const d = new Date();
    d.setDate(d.getDate() - 15);
    return d.toISOString().slice(0, 10);
}
function getDefaultEndDate() {
    const d = new Date();
    return d.toISOString().slice(0, 10);
}

async function fetchIcardTransitions() {
    if (!icardToken) return;
    const begin = document.getElementById('icardBeginDate').value;
    const end = document.getElementById('icardEndDate').value;
    const tableArea = document.getElementById('icardTransitionsTableArea');
    tableArea.innerHTML = '<span class="text-gray-400"><i class="fas fa-spinner fa-spin mr-2"></i>Yükleniyor...</span>';
    try {
    const url = `https://api.icardportal.com/mobile/30/v1/mobile/Transitions?BeginDate=${begin}&EndDate=${end}`;
    const response = await fetch(url, {
        headers: {
        'languagecode': 'tr',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'tr-TR,tr;q=0.9',
        'User-Agent': 'icardv2/258 CFNetwork/3857.100.1 Darwin/25.0.0',
        'Authorization': 'Bearer ' + icardToken,
        }
    });
    if (!response.ok) {
        const err = await response.text();
        throw new Error('Liste alınamadı: ' + err);
    }
    const data = await response.json();
    tableArea.innerHTML = renderIcardTransitionsTable(data.data || []);
    } catch (e) {
    tableArea.innerHTML = `<span class='text-red-400'>Hata: ${e.message}</span>`;
    }
}

function renderIcardTransitionsTable(list) {
    if (!list.length) return '<div class="text-gray-400">Kayıt bulunamadı.</div>';
    let html = `<div class='overflow-x-auto'>
    <table class='min-w-full text-xs md:text-sm text-left border border-gray-700 rounded'>
        <thead class='bg-gray-800'>
        <tr>
            <th class='px-3 py-2'>Tarih</th>
            <th class='px-3 py-2'>Şirket</th>
            <th class='px-3 py-2'>Geçişler</th>
            <th class='px-3 py-2'>Çıkış Saati</th>
            <th class='px-3 py-2'>Süre</th>
        </tr>
        </thead>
        <tbody class='divide-y divide-gray-700'>`;
    for (const row of list) {
    html += `<tr>
        <td class='px-3 py-2 whitespace-nowrap'>${(row.transitionDate||'').slice(0,10)}</td>
        <td class='px-3 py-2'>${row.companyName||''}</td>
        <td class='px-3 py-2'>${(row.details||[]).map(d=>d.transitionTime).join(' / ')}</td>
        <td class='px-3 py-2'>${row.exitTime||'-'}</td>
        <td class='px-3 py-2'>${row.duration||'-'}</td>
    </tr>`;
    }
    html += `</tbody></table></div>`;
    return html;
}


// iCard logout function
function icardLogout() {
    clearIcardLogin();
    icardToken = null;
    // Reset API details area and login form
    const apiDetails = document.getElementById('icardApiDetails');
    if (apiDetails) {
    apiDetails.innerHTML = '<span>Henüz giriş yapılmadı.</span>';
    }
    // Clear password field
    const pw = document.getElementById('icardPassword');
    if (pw) pw.value = '';
}


// Sayfa yüklendiğinde çalışacak fonksiyon
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Admin durumuna göre grid layout'u ayarla
        updateGridLayoutBasedOnAdmin();

        // Sayfa yüklenirken başlangıç durumunu ayarla
        updateUIBasedOnDeviceStatus();

        // Oyun seçim modalı için event listener'ları ekle
        const gameSelectModal = document.getElementById('game-select-modal');
        const closeButton = document.getElementById('game-select-modal-close');

        if (gameSelectModal && closeButton) {
            // Tamam butonuna tıklandığında modalı kapat
            closeButton.addEventListener('click', () => {
                gameSelectModal.classList.add('hidden');
            });

            // Modal dışına tıklandığında modalı kapat
            gameSelectModal.addEventListener('click', (e) => {
                if (e.target === gameSelectModal) {
                    gameSelectModal.classList.add('hidden');
                }
            });
        }

        // Yerel loglar için event listener'ları ekle
        const localLogsTab = document.querySelector('[data-tab="local-logs"]');
        const refreshLocalLogsButton = document.getElementById('refreshLocalLogsButton');
        const localLogsSearchInput = document.getElementById('localLogsSearchInput');
        const localLogsTable = document.getElementById('localLogsTable');
        const localLogsTableBody = document.getElementById('localLogsTableBody');
        const localLogsContextMenu = document.getElementById('localLogsContextMenu');

        if (localLogsTab) {
            // Yerel loglar sekmesine tıklandığında dosyaları yükle
            localLogsTab.addEventListener('click', () => {
                loadLocalLogFiles();
            });
        }

        if (refreshLocalLogsButton) {
            // Yenile butonuna tıklandığında dosyaları yükle
            refreshLocalLogsButton.addEventListener('click', () => {
                loadLocalLogFiles();
            });
        }

        if (localLogsSearchInput) {
            // Arama kutusuna yazıldığında filtreleme yap
            localLogsSearchInput.addEventListener('input', () => {
                filterLocalLogFiles();
            });
        }

        if (localLogsTable) {
            // Tablo başlıklarına tıklandığında sıralama yap
            localLogsTable.querySelectorAll('th[data-sort]').forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.getAttribute('data-sort');

                    // Sıralama yönünü belirle
                    if (localLogsSort.column === column) {
                        localLogsSort.direction = localLogsSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        localLogsSort.column = column;
                        localLogsSort.direction = 'asc';
                    }

                    // Tüm sort ikonlarını resetle
                    localLogsTable.querySelectorAll('th i').forEach(icon => {
                        icon.className = 'fas fa-sort text-gray-500 ml-1';
                    });

                    // Aktif sütunun ikonunu güncelle
                    const icon = header.querySelector('i');
                    icon.className = `fas fa-sort-${localLogsSort.direction === 'asc' ? 'up' : 'down'} text-custom ml-1`;

                    // Verileri sırala ve göster
                    sortLocalLogFiles();
                    displayLocalLogFiles(localLogsData);
                });
            });
        }

        // Çoklu seçim için mouse down eventi
        if (localLogsTableBody) {
            // Mouse down eventi
            localLogsTableBody.addEventListener('mousedown', (e) => {
                // Sağ tık için kontrol ekleyelim
                if (e.button === 2) { // 2 = sağ tık
                    return; // Sağ tıkta seçim işlemini atlayalım
                }

                const row = e.target.closest('tr');
                if (!row || !row.hasAttribute('data-index')) return;

                localLogsIsSelecting = true;
                localLogsStartRowIndex = parseInt(row.dataset.index);
                localLogsLastSelectedIndex = localLogsStartRowIndex;

                // Ctrl tuşuna basılı değilse önceki seçimleri temizle
                if (!e.ctrlKey) {
                    deselectAllLocalLogs();
                }

                toggleLocalLogRowSelection(row);

                // Text seçimini engelle
                e.preventDefault();
            });

            // Mouse move eventi
            localLogsTableBody.addEventListener('mousemove', (e) => {
                if (!localLogsIsSelecting) return;

                const row = e.target.closest('tr');
                if (!row || !row.hasAttribute('data-index')) return;

                const currentIndex = parseInt(row.dataset.index);
                if (currentIndex === localLogsLastSelectedIndex) return;

                // Seçim aralığını güncelle
                const rows = localLogsTableBody.querySelectorAll('tr[data-index]');
                const start = Math.min(localLogsStartRowIndex, currentIndex);
                const end = Math.max(localLogsStartRowIndex, currentIndex);

                rows.forEach((row) => {
                    const rowIndex = parseInt(row.dataset.index);
                    if (rowIndex >= start && rowIndex <= end) {
                        selectLocalLogRow(row);
                    } else if (!e.ctrlKey) {
                        deselectLocalLogRow(row);
                    }
                });

                localLogsLastSelectedIndex = currentIndex;
                updateLocalLogsSelectionButtons();
            });

            // Mouse up eventi
            document.addEventListener('mouseup', () => {
                localLogsIsSelecting = false;
            });
        }

        // Context menü için event listener'lar
        if (localLogsContextMenu) {
            // Sayfa herhangi bir yerine tıklandığında context menüyü gizle
            document.addEventListener('click', (e) => {
                if (!localLogsContextMenu.contains(e.target)) {
                    localLogsContextMenu.classList.add('hidden');
                }
            });

            // "Aç" liste öğesine tıklandığında dosyayı aç
            const openItem = document.querySelector('#localLogsContextOpen');
            if (openItem) {
                openItem.addEventListener('click', () => {
                    const selectedRows = getSelectedLocalLogRows();
                    if (selectedRows.length === 1) {
                        openLocalLogFile(selectedRows[0].filename);
                    }
                    localLogsContextMenu.classList.add('hidden');
                });
            }

            // "Dosya Konumunu Aç" liste öğesine tıklandığında dosya konumunu aç
            const openLocationItem = document.querySelector('#localLogsContextOpenLocation');
            if (openLocationItem) {
                openLocationItem.addEventListener('click', () => {
                    const selectedRows = getSelectedLocalLogRows();
                    if (selectedRows.length === 1) {
                        openLocalLogFileLocation(selectedRows[0].filename);
                    }
                    localLogsContextMenu.classList.add('hidden');
                });
            }

            // "FTP'ye Tekrar Yükle" liste öğesine tıklandığında dosyayı FTP'ye yükle
            const uploadToFtpItem = document.querySelector('#localLogsContextUploadToFtp');
            if (uploadToFtpItem) {
                uploadToFtpItem.addEventListener('click', () => {
                    const selectedRows = getSelectedLocalLogRows();
                    if (selectedRows.length > 0) {
                        if (selectedRows.length === 1) {
                            uploadLogFileToFtp(selectedRows[0].filename);
                        } else {
                            uploadSelectedLogsToFtp();
                        }
                    }
                    localLogsContextMenu.classList.add('hidden');
                });
            }

            // "Sil" liste öğesine tıklandığında dosyayı sil
            const deleteItem = document.querySelector('#localLogsContextDelete');
            if (deleteItem) {
                deleteItem.addEventListener('click', () => {
                    const selectedRows = getSelectedLocalLogRows();
                    if (selectedRows.length > 0) {
                        if (selectedRows.length === 1) {
                            deleteLocalLogFile(selectedRows[0].filename);
                        } else {
                            deleteSelectedLogs();
                        }
                    }
                    localLogsContextMenu.classList.add('hidden');
                });
            }
        }

        // Tümünü seç butonuna tıklandığında
        const selectAllBtn = document.getElementById('selectAllLocalLogs');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', selectAllLocalLogs);
        }

        // Seçimi kaldır butonuna tıklandığında
        const deselectAllBtn = document.getElementById('deselectAllLocalLogs');
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', deselectAllLocalLogs);
        }

        // Seçilenleri FTP'ye yükle butonuna tıklandığında
        const uploadSelectedBtn = document.getElementById('uploadSelectedToFtp');
        if (uploadSelectedBtn) {
            uploadSelectedBtn.addEventListener('click', uploadSelectedLogsToFtp);
        }

        // Seçilenleri sil butonuna tıklandığında
        const deleteSelectedBtn = document.getElementById('deleteSelectedLogs');
        if (deleteSelectedBtn) {
            deleteSelectedBtn.addEventListener('click', deleteSelectedLogs);
        }

        // Başlık checkbox'una tıklandığında
        const headerCheckbox = document.getElementById('headerCheckbox');
        if (headerCheckbox) {
            headerCheckbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    selectAllLocalLogs();
                } else {
                    deselectAllLocalLogs();
                }
            });
        }

        // Atanabilecek kişileri getir
        if (document.getElementById('taskAssignee')) {
            postMessage({
                type: 'getAssignees'
            });
        }

        // Sprint listesini getir
        if (document.getElementById('taskSprint')) {
            postMessage({
                type: 'getSprints'
            });
        }

        // Video-SS tablosu için context menu işlevleri
        setupVideoSsContextMenu();

        // Video-SS tablosu için arama işlevi
        const videoSsSearchInput = document.getElementById('videoSsSearchInput');
        if (videoSsSearchInput) {
            videoSsSearchInput.addEventListener('input', filterVideoSsTable);
        }

                // Tablo başlıklarına tıklama eventi
        document.querySelectorAll('#paymentsTable th[data-sort]').forEach(header => {
            header.addEventListener('click', () => {
                sortPaymentsTable(header.dataset.sort);
            });
        });

        // Arama input eventi
        document.getElementById('paymentSearch').addEventListener('input', refreshPaymentsTable);

        // Checkbox değişim eventi
        document.getElementById('showPending').addEventListener('change', refreshPaymentsTable);
        
        
        // Otomatik Refund checkbox eventi
        document.getElementById('autoRefund').addEventListener('change', (e) => {
            if (e.target.checked) {
                // admin değilse yapamasın.
                if (!adminse()) {
                    showNotification('Bu işlemi yapmaya yetkiniz yok!', 'error');
                    e.target.checked = false;
                    return;
                }
                startAutoRefund();
            } else {
                stopAutoRefund();
            }
        });

        const fileHashSearch = document.getElementById('fileHashSearch');
        const showOnlyChangedFiles = document.getElementById('showOnlyChangedFiles');

        if (fileHashSearch) {
            fileHashSearch.addEventListener('input', function() {
                updateFileHashTable(fileHashData);
            });
        }

        if (showOnlyChangedFiles) {
            showOnlyChangedFiles.addEventListener('change', function() {
                updateFileHashTable(fileHashData);
            });
        }

        // Oyun verileri için olay dinleyicileri
        const gamesDataSearch = document.getElementById('gamesDataSearch');
        const refreshGamesDataButton = document.getElementById('refreshGamesDataButton');

        if (gamesDataSearch) {
            gamesDataSearch.addEventListener('input', function() {
                filterGamesDataTable();
            });
        }

        if (refreshGamesDataButton) {
            refreshGamesDataButton.addEventListener('click', function() {
                getGamesData();
            });
        }

        // Oyun verileri tablosundaki sütun başlıklarına tıklama olayı ekle
        const gamesDataTableHeaders = document.querySelectorAll('#gamesDataTable th[data-sort]');
        gamesDataTableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');
                sortGamesDataTable(column);
            });
        });

        // Oyun verileri sekmesi açıldığında verileri yükle
        const admingamedata = document.querySelector('a[data-admin-tab="game-data"]');
        if (admingamedata) {
        admingamedata.addEventListener('click', function() {
            if (gamesData.length === 0) {
                getGamesData();
            }
        });
        }

        // Yönetici paneli açıldığında oyun verilerini yükle
        document.querySelector('button[data-tab="admin-side"]')?.addEventListener('click', function() {
            setTimeout(() => {
                if (gamesData.length === 0) {
                    //getGamesData();
                }
            }, 500); // Yönetici paneli açıldıktan sonra kısa bir süre bekle
        });

        // Oyun Verileri sekmesi açıldığında oyun verilerini yükle
        document.querySelector('button[data-tab="game-data"]')?.addEventListener('click', function() {
            setTimeout(() => {
                if (gamesData.length === 0) {
                    getGamesData();
                }
            }, 500); // Oyun Verileri sekmesi açıldıktan sonra kısa bir süre bekle
        });
        // Yeni oyun ekleme butonuna tıklama olayı ekle
        const addGameButton = document.getElementById('addGameButton');
        if (addGameButton) {
            addGameButton.addEventListener('click', function() {
                openGameEditModal(); // Boş parametre ile yeni oyun ekleme modunu aç
            });
        }

        // Tablo başlıklarına tıklama olayı ekle
        document.querySelectorAll('#fileHashTable th[data-sort]').forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');

                // Aynı sütuna tıklandıysa sıralama yönünü değiştir
                if (column === currentSortColumn) {
                    currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSortColumn = column;
                    currentSortDirection = 'asc';
                }

                // Sıralama ikonlarını güncelle
                updateSortIcons(column);

                // Tabloyu güncelle
                updateFileHashTable(fileHashData);
            });
        });

        // File Hasher tablosu için sağ tık menüsü
        const fileHashTableBody = document.getElementById('fileHashTableBody');
        const fileHashContextMenu = document.getElementById('fileHashContextMenu');

        if (fileHashTableBody && fileHashContextMenu) {
            // Zaten kurulmuş mu kontrol et
            if (fileHashTableBody.hasAttribute('data-context-menu-setup')) {
                return;
            }

            // Kurulduğunu işaretle
            fileHashTableBody.setAttribute('data-context-menu-setup', 'true');

            // Sağ tık olayı
            fileHashTableBody.addEventListener('contextmenu', (e) => {
                e.preventDefault();

                const clickedRow = e.target.closest('tr');
                if (!clickedRow) return;

                // Tıklanan satırın verilerini al
                const cells = clickedRow.querySelectorAll('td');
                if (cells.length < 4) return;

                currentFileHashItem = {
                    id: cells[0].textContent.trim(),
                    filehash: cells[1].textContent.trim(),
                    path: cells[2].textContent.trim(),
                    filename: cells[3].textContent.trim(),
                    status: cells[4].querySelector('span') ? cells[4].querySelector('span').textContent.trim() : ''
                };

                // Bağlam menüsünü konumlandır
                positionContextMenu(fileHashContextMenu, e.clientX, e.clientY);
                fileHashContextMenu.classList.remove('hidden');
            });

            // Sayfa herhangi bir yerine tıklandığında bağlam menüsünü gizle
            document.addEventListener('click', (e) => {
                if (!fileHashContextMenu.contains(e.target)) {
                    fileHashContextMenu.classList.add('hidden');
                }
            });

            // "Dosya Konumunu Aç" menü öğesine tıklanırsa
            const openLocationButton = document.getElementById('fileHashContextOpen');
            openLocationButton.addEventListener('click', () => {
                if (currentFileHashItem) {
                    const filePath = currentFileHashItem.path;
                    const fileName = currentFileHashItem.filename;

                    postMessage({
                        type: 'openFileLocation',
                        path: filePath,
                        filename: fileName
                    });
                }
                fileHashContextMenu.classList.add('hidden');
            });

            // "Dosya Yolunu Kopyala" menü öğesine tıklanırsa
            const copyPathButton = document.getElementById('fileHashContextCopy');
            copyPathButton.addEventListener('click', () => {
                if (currentFileHashItem) {
                    const fullPath = currentFileHashItem.path + '/' + currentFileHashItem.filename;

                    // Dosya yolunu JavaScript ile kopyala
                    copyTextToClipboard(fullPath);

                    showNotification('Dosya yolu panoya kopyalandı.');
                }
                fileHashContextMenu.classList.add('hidden');
            });

            // "FTP'ye Yükle" menü öğesine tıklanırsa
            const uploadFtpButton = document.getElementById('fileHashContextUploadFtp');
            uploadFtpButton.addEventListener('click', () => {
                if (currentFileHashItem) {
                    const filePath = currentFileHashItem.path;
                    const fileName = currentFileHashItem.filename;

                    postMessage({
                        type: 'uploadFileToFtp',
                        path: filePath,
                        filename: fileName
                    });
                }
                fileHashContextMenu.classList.add('hidden');
            });
        }

        // Admin tab'a tıklandığında kullanıcı verilerini getir
        const adminTabButton = document.querySelector('[data-tab="admin-side"]');
        if (adminTabButton) {
            adminTabButton.addEventListener('click', () => {
                // Admin tab'a tıklandığında user-data tab'ını aktif et
                setTimeout(() => {
                    switchAdminTab('user-data');
                    getUserData();
                }, 100);
            });
        }

        // Kullanıcı verileri arama kutusu
        const userDataSearch = document.getElementById('userDataSearch');
        if (userDataSearch) {
            userDataSearch.addEventListener('input', (e) => {
                filterUserData(e.target.value);
            });
        }

        // Kullanıcı verileri yenile butonu
        const refreshUserDataButton = document.getElementById('refreshUserDataButton');
        if (refreshUserDataButton) {
            refreshUserDataButton.addEventListener('click', getUserData);
        }

        // Yeni kullanıcı ekle butonu
        const addUserButton = document.getElementById('addUserButton');
        if (addUserButton) {
            addUserButton.addEventListener('click', () => openUserModal('add'));
        }

        // Sürüm verileri arama kutusu
        const versionSearch = document.getElementById('versionSearch');
        if (versionSearch) {
            versionSearch.addEventListener('input', (e) => {
                filterVersionData(e.target.value);
            });
        }

        // Sürüm verileri yenile butonu
        const refreshVersionButton = document.getElementById('refreshVersionButton');
        if (refreshVersionButton) {
            refreshVersionButton.addEventListener('click', getVersionData);
        }

        // Sürüm tablosu başlıklarına tıklama ile sıralama
        const versionTable = document.getElementById('versionTable');
        if (versionTable) {
            const headers = versionTable.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.getAttribute('data-sort');
                    if (versionDataSort.column === column) {
                        versionDataSort.direction = versionDataSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        versionDataSort.column = column;
                        versionDataSort.direction = 'asc';
                    }

                    // Sıralama ikonlarını güncelle
                    headers.forEach(h => {
                        const icon = h.querySelector('i');
                        if (h.getAttribute('data-sort') === versionDataSort.column) {
                            icon.className = versionDataSort.direction === 'asc' ? 'fas fa-sort-up text-custom ml-1' : 'fas fa-sort-down text-custom ml-1';
                        } else {
                            icon.className = 'fas fa-sort text-gray-500 ml-1';
                        }
                    });

                    renderVersionTable();
                });
            });
        }

        // Studio QA verileri arama kutusu
        const studioQASearch = document.getElementById('studioQASearch');
        if (studioQASearch) {
            studioQASearch.addEventListener('input', (e) => {
                filterStudioQAData(e.target.value);
            });
        }

        // Studio QA verileri yenile butonu
        const refreshStudioQAButton = document.getElementById('refreshStudioQAButton');
        if (refreshStudioQAButton) {
            refreshStudioQAButton.addEventListener('click', getStudioQAData);
        }

        // Yeni Studio QA kaydı ekle butonu
        const addStudioQAButton = document.getElementById('addStudioQAButton');
        if (addStudioQAButton) {
            addStudioQAButton.addEventListener('click', () => openStudioQAModal('add'));
        }

        // Studio QA modal butonları
        const studioQAModalSave = document.getElementById('studioQAModalSave');
        const studioQAModalCancel = document.getElementById('studioQAModalCancel');
        if (studioQAModalSave) {
            studioQAModalSave.addEventListener('click', saveStudioQA);
        }
        if (studioQAModalCancel) {
            studioQAModalCancel.addEventListener('click', closeStudioQAModal);
        }

        // Studio QA silme modal butonları
        const deleteStudioQAConfirm = document.getElementById('deleteStudioQAConfirm');
        const deleteStudioQACancel = document.getElementById('deleteStudioQACancel');
        if (deleteStudioQAConfirm) {
            deleteStudioQAConfirm.addEventListener('click', deleteStudioQA);
        }
        if (deleteStudioQACancel) {
            deleteStudioQACancel.addEventListener('click', closeDeleteStudioQAModal);
        }

        // Studio QA tablosu başlıklarına tıklama ile sıralama
        const studioQATable = document.getElementById('studioQATable');
        if (studioQATable) {
            const headers = studioQATable.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.getAttribute('data-sort');
                    if (studioQADataSort.column === column) {
                        studioQADataSort.direction = studioQADataSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        studioQADataSort.column = column;
                        studioQADataSort.direction = 'asc';
                    }

                    // Sıralama ikonlarını güncelle
                    headers.forEach(h => {
                        const icon = h.querySelector('i');
                        if (h.getAttribute('data-sort') === studioQADataSort.column) {
                            icon.className = studioQADataSort.direction === 'asc' ? 'fas fa-sort-up text-custom ml-1' : 'fas fa-sort-down text-custom ml-1';
                        } else {
                            icon.className = 'fas fa-sort text-gray-500 ml-1';
                        }
                    });

                    renderStudioQATable();
                });
            });
        }

        // Data Viewer verileri arama kutusu
        const dataViewerSearch = document.getElementById('dataViewerSearch');
        if (dataViewerSearch) {
            dataViewerSearch.addEventListener('input', (e) => {
                filterDataViewerData(e.target.value);
            });
        }

        // Data Viewer verileri yenile butonu
        const refreshDataViewerButton = document.getElementById('refreshDataViewerButton');
        if (refreshDataViewerButton) {
            refreshDataViewerButton.addEventListener('click', getDataViewerFiles);
        }

        // Mouse up olayını document'a ekle (sürükleyerek seçim için)
        document.addEventListener('mouseup', () => {
            dataViewerIsMouseDown = false;
        });

        // Mouse leave olayını tablo için ekle (sürükleyerek seçim için)
        const dataViewerTableContainer = document.querySelector('#admin-dataviewer .bg-gray-700');
        if (dataViewerTableContainer) {
            dataViewerTableContainer.addEventListener('mouseleave', () => {
                dataViewerIsMouseDown = false;
            });
        }

        // Data Viewer tablosu başlıklarına tıklama ile sıralama
        const dataViewerTable = document.getElementById('dataViewerTable');
        if (dataViewerTable) {
            const headers = dataViewerTable.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.getAttribute('data-sort');
                    if (dataViewerDataSort.column === column) {
                        dataViewerDataSort.direction = dataViewerDataSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        dataViewerDataSort.column = column;
                        dataViewerDataSort.direction = 'asc';
                    }

                    // Sıralama ikonlarını güncelle
                    headers.forEach(h => {
                        const icon = h.querySelector('i');
                        if (h.getAttribute('data-sort') === dataViewerDataSort.column) {
                            icon.className = dataViewerDataSort.direction === 'asc' ? 'fas fa-sort-up text-custom ml-1' : 'fas fa-sort-down text-custom ml-1';
                        } else {
                            icon.className = 'fas fa-sort text-gray-500 ml-1';
                        }
                    });

                    renderDataViewerTable();
                });
            });

            // Tümünü seç checkbox'una olay dinleyicisi ekle
            const selectAllCheckbox = document.getElementById('dataViewerSelectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', toggleSelectAllDataViewerFiles);
            }

            // Sağ tık menüsü butonlarına olay dinleyicileri ekle
            const contextOpen = document.getElementById('dataViewerContextOpen');
            const contextArchive = document.getElementById('dataViewerContextArchive');

            if (contextOpen) {
                contextOpen.addEventListener('click', () => {
                    if (selectedDataViewerFiles.length > 0) {
                        // Çoklu seçimde ilk dosyayı aç
                        downloadAndOpenFile(selectedDataViewerFiles[0]);
                    }
                });
            }

            if (contextArchive) {
                contextArchive.addEventListener('click', archiveSelectedFiles);
            }
        }

        // Tablo başlıklarına tıklama ile sıralama
        const userDataTable = document.getElementById('userDataTable');
        if (userDataTable) {
            const headers = userDataTable.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.getAttribute('data-sort');
                    if (userDataSort.column === column) {
                        userDataSort.direction = userDataSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        userDataSort.column = column;
                        userDataSort.direction = 'asc';
                    }

                    // Sıralama ikonlarını güncelle
                    headers.forEach(h => {
                        const icon = h.querySelector('i');
                        if (h.getAttribute('data-sort') === userDataSort.column) {
                            icon.className = userDataSort.direction === 'asc' ? 'fas fa-sort-up text-custom ml-1' : 'fas fa-sort-down text-custom ml-1';
                        } else {
                            icon.className = 'fas fa-sort text-gray-500 ml-1';
                        }
                    });

                    renderUserDataTable();
                });
            });
        }

        // NotificationManager instance'ını burada oluştur
        notificationManager = new NotificationManager();

        // Setup storage location functionality
        const storageSelect = document.getElementById('storageSelect');
        const saveStorageBtn = document.getElementById('saveStorageLocation');
        // Save storage location
        saveStorageBtn.addEventListener('click', function() {
            const selectedLocation = storageSelect.value;
            window.chrome.webview.postMessage({
                type: "saveStorageLocation",
                location: selectedLocation
            });
        });
            
        getStorageLocations();
            
        // Request current folder location on page load
        window.chrome.webview.postMessage({
            type: "getCurrentFolderLocation"
        });

        // Başlangıçta header'ı gizle
        document.querySelector('header').classList.add('hidden');
        // Tüm main elementlerini gizle
        document.querySelectorAll('main').forEach(main => {
            main.classList.add('hidden');
        });
        // Sadece login ekranını göster
        document.getElementById('login').classList.remove('hidden');
        // Footer'ı gizle
        document.querySelector('footer').classList.add('hidden');
        // Event listener'ları ayarla
        setupEventListeners();

        // QA Task ile ilgili event listener'ları ayarla
        setupQaTaskEventListeners();

        // All Tasks ile ilgili event listener'ları ayarla
        setupAllTasksEventListeners();
        // OS seçimi değiştiğinde
        function onOSChange(value) {
            // OS değişikliğini C# tarafına bildir
            postMessage({
                type: 'osSelected',
                value: value
            });

            // Global OS değişkenini güncelle
            currentOS = value;

            // Bildirim bayraklarını sıfırla
            disconnectionNotificationShown = false;
            connectionNotificationShown = false;

            // UI'yi güncelle
            updateUIBasedOnDeviceStatus();
        }
        // OS seçim elementini dinle
        const osSelect = document.getElementById('os-select');
        if (osSelect) {
            osSelect.addEventListener('change', (e) => {
                onOSChange(e.target.value);
            });
        }
        const searchInput = document.getElementById("videoSsSearchInput");
        const refreshButton = document.getElementById("refreshVideoSsButton");
        const table = document.getElementById("videoSsTable");
        const tableBody = document.getElementById("videoSsTableBody");
        // Her sütun için sıralama durumunu tutan nesne (default: artan)
        const sortState = {};
        // Belirli bir sütun indeksine göre tabloyu sıralayan fonksiyon
        function sortTableByColumn(columnIndex, asc = true) {
            const rows = Array.from(tableBody.querySelectorAll("tr"));
            rows.sort((a, b) => {
                const aText = a.children[columnIndex].textContent.trim().toLowerCase();
                const bText = b.children[columnIndex].textContent.trim().toLowerCase();
                // Sayısal veriler için karşılaştırma
                const aNum = parseFloat(aText);
                const bNum = parseFloat(bText);
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return asc ? aNum - bNum : bNum - aNum;
                }
                return asc ? aText.localeCompare(bText) : bText.localeCompare(aText);
            });
            // Sıralanmış satırları tekrar ekleyelim
            rows.forEach(row => tableBody.appendChild(row));
        }
        // Tablo başlıklarına tıklanma olayı ekleyelim
        const headers = table.querySelectorAll("th");
        headers.forEach((th, index) => {
            sortState[index] = true; // varsayılan artan sıralama
            th.addEventListener("click", function() {
                sortState[index] = !sortState[index]; // her tıkta sıralama yönünü ters çevir
                sortTableByColumn(index, sortState[index]);
            });
        });

        // Arama alanı: Kullanıcı yazdıkça tablonun satırlarını filtrele
        searchInput.addEventListener("input", function() {
            const filter = searchInput.value.toLowerCase();
            const rows = tableBody.getElementsByTagName("tr");
            let visibleCount = 0;

            // Önce tüm satırları tarayıp, arama kriterine uyanları gösteriyoruz.
            Array.from(rows).forEach(row => {
                // Eğer satırda daha önceden "no-result-row" sınıfı eklenmişse, onu atla.
                if (row.classList.contains("no-result-row")) return;

                const cells = row.getElementsByTagName("td");
                let rowContainsFilter = false;
                Array.from(cells).forEach(cell => {
                    if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                        rowContainsFilter = true;
                    }
                });
                if (rowContainsFilter) {
                    row.style.display = "";
                    visibleCount++;
                } else {
                    row.style.display = "none";
                }
            });

            // Daha önce eklenmiş "no result" satırı varsa, temizleyelim.
            const existingNoResultRow = tableBody.querySelector(".no-result-row");
            if (existingNoResultRow) {
                existingNoResultRow.remove();
            }

            if (visibleCount === 0) {
                const noResultRow = document.createElement("tr");
                noResultRow.classList.add("no-result-row");
                const cell = document.createElement("td");
                cell.colSpan = 5;
                cell.classList.add("text-center", "px-4", "py-2", "border-b", "border-gray-600", "text-gray-300");
                cell.textContent = "Aradığın veri yok";
                noResultRow.appendChild(cell);
                tableBody.appendChild(noResultRow);
            }
        });

        // Yenile butonuna tıklandığında C# tarafına mesaj gönder
        refreshButton.addEventListener("click", function() {
            postMessage({
                type: "tabChanged",
                tab: "video-ss"
            });
        });

        // Tablo gövdesi için tıklama olaylarını delege ederek, dosya adlarının tıklanabilirliğini sağlıyoruz.
        tableBody.addEventListener("click", function(e) {
            const target = e.target;
            if (target.classList.contains("file-link")) {
                e.preventDefault();
                const fileName = target.getAttribute("data-filename");
                handleFileNameClick(fileName);
            }
        });
        // Context menu elementini al
        const contextMenu = document.getElementById("videoSsContextMenu");

        // 1. Tabloda sağ tık (contextmenu) olayı
        // tableBody.addEventListener("contextmenu", function(e) {
        //     e.preventDefault(); // Varsayılan tarayıcı sağ tık menüsünü engelle

        //     // Tıklanan elemandan itibaren en yakın TR etiketine kadar yukarı çık
        //     let target = e.target;
        //     while (target && target.nodeName !== "TR") {
        //         target = target.parentNode;
        //     }
        //     if (!target) return; // TR bulunamadıysa (boş alana tıklanmışsa) menü açma

        //     // Bu satırın data-filename özelliğini alalım
        //     currentFilename = target.getAttribute("data-filename") || "Bilinmiyor";

        //     // Menüyü mouse konumuna yerleştir
        //     contextMenu.style.top = e.pageY + "px";
        //     contextMenu.style.left = e.pageX + "px";
        //     contextMenu.classList.remove("hidden");
        // });
        // 2. Sayfada herhangi bir yere tıklandığında menüyü gizle
        document.addEventListener("click", function(e) {
            // Eğer tıklanan yer menü bileşenlerinin içinde değilse menüyü gizle
            if (!contextMenu.contains(e.target)) {
                contextMenu.classList.add("hidden");
            }
        });

    } catch (error) {
        console.error('Başlangıç verilerini alma hatası:', error);
    }
    try{
        
      // iCard: C# Settings ile otomatik login
      let autoLoginDone = false;
      if (window.external && typeof window.external.GetIcardLogin === 'function') {
        try {
          const creds = window.external.GetIcardLogin(); // { username: '', password: '' }
          if (creds && creds.username && creds.password) {
            const userInput = document.getElementById('icardUsername');
            const pwInput = document.getElementById('icardPassword');
            if (userInput) userInput.value = creds.username;
            if (pwInput) pwInput.value = creds.password;
            icardLogin();
            autoLoginDone = true;
          }
        } catch (e) { /* ignore */ }
      }
      if (!autoLoginDone) {
        // localStorage ile eski otomatik login
        const login = loadIcardLogin();
        if (login.username) {
          const userInput = document.getElementById('icardUsername');
          if (userInput) userInput.value = login.username;
        }
        if (login.token) {
          icardToken = login.token;
          // Show logged-in state
          const apiDetails = document.getElementById('icardApiDetails');
          if (apiDetails) {
            apiDetails.innerHTML = `<div class='text-green-400 mb-4 flex items-center gap-2'><i class="fas fa-check-circle"></i> Giriş başarılı!</div>
              <div class='flex flex-wrap gap-4 mb-4 items-end'>
                <div>
                  <label class='block text-xs mb-1'>Başlangıç Tarihi</label>
                  <input type='date' id='icardBeginDate' class='bg-gray-700 border border-gray-600 rounded px-2 py-1 text-gray-100' value='${getDefaultBeginDate()}'>
                </div>
                <div>
                  <label class='block text-xs mb-1'>Bitiş Tarihi</label>
                  <input type='date' id='icardEndDate' class='bg-gray-700 border border-gray-600 rounded px-2 py-1 text-gray-100' value='${getDefaultEndDate()}'>
                </div>
                <button onclick='fetchIcardTransitions()' class='bg-custom hover:bg-custom/90 px-4 py-2 rounded text-white font-semibold transition ml-2'>Geçişleri Listele</button>
                <button onclick='icardLogout()' class='bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded text-gray-200 font-semibold transition ml-2 border border-gray-600'>Çıkış Yap</button>
              </div>
              <div id='icardTransitionsTableArea' class='rounded-xl bg-gray-900 border border-gray-700 p-4'></div>`;
            fetchIcardTransitions();
          }
        }
      }
      // Login button and Enter key
      const btn = document.getElementById('icardLoginBtn');
      if (btn) btn.onclick = icardLogin;
      const pw = document.getElementById('icardPassword');
      if (pw) pw.addEventListener('keydown', function(e) { if (e.key === 'Enter') icardLogin(); });
    }catch (error){
        console.error('Otomatik giriş hatası');
    }
});
// Sayfa yenilemeyi engellemek için klavye olaylarını dinle (capture phase'de yakalayarak daha erken müdahale ediyoruz)
document.addEventListener('keydown', function(e) {
    // F5 tuşu veya Ctrl+R kombinasyonu
    if (e.key === 'F5' || (e.ctrlKey && e.key === 'r') || e.keyCode === 116 || (e.ctrlKey && e.keyCode === 82)) {
        e.preventDefault();
        e.stopPropagation();
        
        return false;
    }
}, true); // true parametresi capture phase'de yakalamayı sağlıyor

// Tarayıcı yenileme butonunu devre dışı bırak
window.addEventListener('beforeunload', function(e) {
    // Modern tarayıcılarda sadece preventDefault yeterli
    e.preventDefault();
    // Eski tarayıcılar için boş string döndür
    return '';
});
// Sayfa yüklendiğinde de F5 tuşunu engelle (ekstra güvenlik)
window.onload = function() {
    // F5 tuşunu engelle
    document.onkeydown = function(e) {
        if (e.key === 'F5' || e.keyCode === 116 || (e.ctrlKey && (e.key === 'r' || e.keyCode === 82))) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    };
};
window.chrome.webview.addEventListener('message', event => {
  // Sadece belirli mesaj tipleri için console.log göster, 'updateStatus' tipindeki mesajları gösterme
  if (event.data.type !== 'updateStatus') {
    
  }
  handleTestRailMessage(event.data);

  // File hash data handling
  if (event.data.type === 'fileHashData') {
    updateFileHashTable(event.data.data);
  } else if (event.data.type === 'gamesData') {
    // Oyun verilerini işle
    gamesData = event.data.data;
    displayGamesData(gamesData);
  } else if (event.data.type === 'versionData') {
    // Sürüm verilerini işle
    loadVersionDataToTable(event.data.data);
  } else if (event.data.type === 'studioQAData') {
    // Studio QA verilerini işle
    loadStudioQADataToTable(event.data.data);
  } else if (event.data.type === 'dataViewerFiles') {
    // Data Viewer dosyalarını işle
    loadDataViewerFilesToTable(event.data.data);
  } else if (event.data.type === 'qaTasksData') {
    // QA Task verilerini işle
    hideLoadingIndicator('qaTaskTableBody');
    displayQaTasks(event.data.tasks);
  } else if (event.data.type === 'studioQATasksData') {
    // Studio QA Tasks verilerini işle
    hideLoadingIndicator('studioQATasksTableBody');
    displayStudioQATasks(event.data.tasks);
  } else if (event.data.type === 'allTasksData') {
    // All Tasks verilerini işle
    hideLoadingIndicator('allTasksTableBody');
    displayAllTasks(event.data.tasks);
  } else if (event.data.type === 'qaTaskStatusesData') {
    // QA Task durumlarını işle
    displayQaTaskStatuses(event.data.statuses);
    // All Tasks için de aynı durumları kullan
    displayAllTasksStatuses(event.data.statuses);
  } else if (event.data.type === 'qaTaskPrioritiesData') {
    // QA Task önceliklerini işle
    qaTaskPriorities = event.data.priorities;
  } else if (event.data.type === 'qaTaskUsersData') {
    // QA Task kullanıcılarını işle
    displayQaTaskUsers(event.data.users);
  } else if (event.data.type === 'profileData') {
    // Profil verilerini işle
    updateProfileDisplay(event.data.data);
    showNotification('Profil verileri güncellendi.', 'success');
  } else if (event.data.type === 'archiveFilesResult') {
    // Arşivleme işlemi sonucunu işle
    const success = event.data.success;
    const message = event.data.message;
    const status = success ? 'success' : 'error';

    showNotification(message, status);

    if (success) {
      // Başarılı arşivleme sonrası dosya listesini yenile
      getDataViewerFiles();
      // Seçili dosyaları temizle
      selectedDataViewerFiles = [];
    }
  }
  else if (event.data.type === 'initSettings') {
    const alwaysTopCheckbox = document.getElementById('always-top');
    const autoBundleCheckbox = document.getElementById('auto-bundle');
    const videoRecordLoopCheckbox = document.getElementById('videorecordloop');

    if (alwaysTopCheckbox) {
      alwaysTopCheckbox.checked = event.data.data.alwaysTop;
    }
    if (autoBundleCheckbox) {
      autoBundleCheckbox.checked = event.data.data.autoBundle;
    }
    if (videoRecordLoopCheckbox && event.data.data.hasOwnProperty('continuousRecording')) {
      videoRecordLoopCheckbox.checked = event.data.data.continuousRecording;
    }

    document.getElementById('version-info').textContent = `Version: ${event.data.data.version}`;
    document.getElementById('status-info').textContent = `Status: ${event.data.data.status}`;
    document.getElementById('status-info').className = `text-${event.data.data.status === 'Ready' ? 'green' : 'yellow'}-500`;

    // Don't call setupEventListeners() here - it's already called during initialization
  } else if (event.data.type === 'updateStatus') {
    document.getElementById('status-info').textContent = `Status: ${event.data.status}`;
    if (event.data.color){
        document.getElementById('status-info').className = `text-${event.data.color}-500`;
    }else{
        document.getElementById('status-info').className = `text-${event.data.status === 'Ready' || event.data.status === 'Hazır' ? 'green' : 'yellow'}-500`;
    }
  } else if (event.data.type === 'userPreferences') {
    // Kullanıcı tercihlerini işle
    const isDarkMode = event.data.isDarkMode;
    const soundNotificationsEnabled = event.data.soundNotificationsEnabled;
    
    // Tercihleri uygula
    if (isDarkMode) {
      document.documentElement.classList.add('dark-mode');
      if (darkModeToggleIndicator) {
        darkModeToggleIndicator.classList.add('translate-x-6');
      }
    } else {
      document.documentElement.classList.remove('dark-mode');
      if (darkModeToggleIndicator) {
        darkModeToggleIndicator.classList.remove('translate-x-6');
      }
    }
    
    // Sesli bildirimleri ayarla
    window.soundNotificationsEnabled = soundNotificationsEnabled;
    if (soundNotificationsToggleIndicator) {
      if (soundNotificationsEnabled) {
        soundNotificationsToggleIndicator.classList.add('translate-x-6');
      } else {
        soundNotificationsToggleIndicator.classList.remove('translate-x-6');
      }
    }
    
    console.log('User preferences applied:', { isDarkMode, soundNotificationsEnabled });
  } else if (event.data.type === 'loginSuccess') {
    document.getElementById('login').classList.add('hidden');
    document.querySelector('header').classList.remove('hidden');
    document.getElementById('actions').classList.remove('hidden');
    document.querySelector('footer').classList.remove('hidden');
    // Don't call setupEventListeners() here - it's already called during initialization
  } else if (event.data.type === 'gameList') {
    
    const gameSelect = document.getElementById('gameSelect');

    // Store the current selection if any
    const currentSelection = gameSelect.value;

    // Clear the dropdown
    gameSelect.innerHTML = '';

    // Add a default option
    const defaultOption = document.createElement('option');
    defaultOption.text = 'Oyun Seçilmedi';
    defaultOption.value = 'Oyun Seçilmedi';
    //gameSelect.add(defaultOption);

    // Oyunları alfabetik olarak sırala
    const sortedGames = [...event.data.games].sort((a, b) => a.localeCompare(b));
    sortedGames.forEach(game => {
      const option = document.createElement('option');
      option.text = game;
      option.value = game;
      gameSelect.add(option);
    });

    // Restore previous selection if it exists in the new list
    if (currentSelection && sortedGames.includes(currentSelection)) {
      gameSelect.value = currentSelection;
    }
  } else if (event.data.type === 'updateStudioQA') {
    const table = document.getElementById('studio-qa-table');
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';
    const headerRow = document.createElement('tr');
    headerRow.classList.add('bg-gray-800', 'border-b', 'border-gray-600');
    event.data.data.forEach(item => {
      const th = document.createElement('th');
      th.classList.add('p-3');
      th.textContent = item.title;
      headerRow.appendChild(th);
    });
    const dataRow = document.createElement('tr');
    dataRow.classList.add('border-b', 'border-gray-600');
    event.data.data.forEach(item => {
      const td = document.createElement('td');
      td.classList.add('p-1');
      td.textContent = item.value;
      dataRow.appendChild(td);
    });
    tbody.appendChild(dataRow);
  } else if (event.data.type === 'loginResult') {
    const loginButton = document.querySelector('button[onclick="handleLogin()"]');
    loginButton.disabled = false;
    loginButton.textContent = 'Giriş Yap';

    if (event.data.success) {
      isAdmin = event.data.adminmi; // Admin durumunu kaydet
      userRole = event.data.adminmi ? 'admin' : 'tester'; // Kullanıcı rolünü kaydet

      document.querySelector('header').classList.remove('hidden');
      document.getElementById('login').classList.add('hidden');
      document.getElementById('actions').classList.remove('hidden');
      document.querySelector('footer').classList.remove('hidden');
      document.getElementById('loginUserName').textContent = event.data.username;
      userName = event.data.username;
      userId = event.data.userId;

      // Admin değilse ilgili bölümleri tamamen kaldır
      console.log("isAdmin",isAdmin);
      if (!isAdmin) {
        // Admin Komutları bölümünü kaldır
        const adminCommands = document.getElementById('admin-commands');
        if (adminCommands) {
          adminCommands.remove();
        }

        // Yönetici Paneli bölümünü kaldır
        const adminPanel = document.getElementById('admin-side');
        if (adminPanel) {
          adminPanel.remove();
        }

        // Header'daki Yönetici Paneli butonunu kaldır
        const adminPanelButton = document.querySelector('[data-panel="admin-side"]');
        if (adminPanelButton) {
          adminPanelButton.remove();
        }

        // Yönetici Paneli düğmesini kaldır
        const adminButton = document.querySelector('button[onclick="showAdminPanel()"]');
        if (adminButton) {
          adminButton.remove();
        }

        // Grid yapısını güncelle
        const actionsGrid = document.getElementById('actions');
        if (actionsGrid) {
          actionsGrid.classList.remove('grid-cols-4'); // Tailwind class'ını kaldır
          actionsGrid.classList.add('grid-cols-3'); // Yeni Tailwind class'ı ekle
          actionsGrid.style.gridTemplateColumns = 'repeat(3, minmax(0, 1fr))'; // Inline CSS güncelleme
        }
      }else{
        const adminPanelButton = document.querySelector('[data-tab="admin-side"]');
        if (adminPanelButton) {
          adminPanelButton.classList.remove('hidden');
        }
      }
    } else {

      const errorDiv = document.createElement('div');
      errorDiv.className = 'text-red-500 text-sm mt-2';
      errorDiv.textContent = event.data.message;
      const oldError = document.querySelector('.text-red-500');
      if (oldError) oldError.remove();
      loginButton.parentElement.appendChild(errorDiv);
      setTimeout(() => errorDiv.remove(), 3000);
    }
  } else if (event.data.type === 'updateDeviceStatus') {
    const deviceStatus = event.data.connected ? 'Connected' : 'Disconnected';
    const statusColor = event.data.connected ? 'green' : 'red';
    const connectionType = event.data.wireless ? ' (Wireless)' : event.data.connected ? ' (USB)' : '';
    
    document.getElementById('device-status').textContent = `Device: ${deviceStatus}${connectionType}`;
    document.getElementById('device-status').className = `text-${statusColor}-500`;

    // Cihaz bağlantı durumunu güncelle ve UI'yi buna göre ayarla
    isDeviceConnected = event.data.connected;
    updateUIBasedOnDeviceStatus();
  } else if (event.data.type === 'updateWirelessButtonState') {
    // Kablosuz bağlantı düğmesinin durumunu güncelle
    isWirelessConnected = event.data.isWireless; // Global değişkeni güncelle
    const wirelessButton = document.getElementById('wirelessButton');
    if (wirelessButton) {
      if (event.data.isWireless) {
        // Kablosuz bağlantı aktif - yeşil yap
        wirelessButton.className = 'btn btn-success text-sm';
        wirelessButton.textContent = 'Kablosuz Bağlantıyı Kes';
      } else {
        // Kablosuz bağlantı değil - mavi yap
        wirelessButton.className = 'btn btn-primary text-sm';
        wirelessButton.textContent = 'Kablosuz Bağlantıya Geç';
      }
    }
  } else if (event.data.type === 'updateDeviceInfo') {
    document.getElementById('device-brand').textContent = event.data.data.Brand;
    document.getElementById('device-model').textContent = event.data.data.Model;
    document.getElementById('device-version').textContent = event.data.data.Version;
    document.getElementById('device-chipset').textContent = event.data.data.Chipset;
    document.getElementById('device-cpu').textContent = event.data.data.Cpu;
    document.getElementById('device-resolution').textContent = event.data.data.Resolution;
    document.getElementById('device-language').textContent = event.data.data.Language;
  } else if (event.data.type === 'bundleInfo') {
    document.getElementById('app-version').textContent = event.data.bundleInfo.version;
    document.getElementById('build-number').textContent = event.data.bundleInfo.buildNumber;
    document.getElementById('bundle-id').textContent = event.data.bundleInfo.bundleId;
    document.getElementById('target-api').textContent = event.data.bundleInfo.targetApi;
  } else if (event.data.type === 'localLogFiles') {
    // Yerel log dosyalarını göster
    displayLocalLogFiles(event.data.files);
  } else if (event.data.type === 'localLogFileDeleted') {
    // Dosya silindiğinde bildirim göster ve listeyi yenile
    showNotification(`${event.data.filename} dosyası silindi`, 'success');
    loadLocalLogFiles();
  } else if (event.data.type == 'updateLogButton') {
    document.getElementById('startLogRecordingButton').textContent = event.data.text;
    if (event.data.timerReset) {
      timerStop();
      // Reset the timer display to 00:00
      const logTimerElement = document.getElementById('logTimer');
      if (logTimerElement) {
        logTimerElement.textContent = '00:00';
      }
    }
  } else if (event.data.type === "videoSsData") {
    const data = event.data.data; // Video ve SS verilerini içeren dizi
    const tableBody = document.getElementById("videoSsTableBody");
    const searchInput = document.getElementById("videoSsSearchInput");

    // Global videoSsData değişkenine ata
    videoSsData = data;

    // Verileri tarih sütununa göre sırala (en yeni en üste)
    videoSsData.sort((a, b) => {
      const dateA = parseDateValue(a.Tarih);
      const dateB = parseDateValue(b.Tarih);
      return dateB - dateA; // Azalan sıralama (en yeni en üste)
    });

    // Önce tablo içeriğini temizleyelim
    tableBody.innerHTML = "";

    if (data.length === 0) {
      // Eğer veri yoksa, 6 sütunu kapsayan bir satır ekleyelim
      const row = document.createElement("tr");
      const cell = document.createElement("td");
      cell.colSpan = 6;
      cell.classList.add("text-center", "px-4", "py-3", "text-gray-400");
      cell.textContent = "Veri Yok";
      row.appendChild(cell);
      tableBody.appendChild(row);
    } else {
      // Her veri öğesi için satır oluşturuyoruz
      data.forEach((item, index) => {
        const row = document.createElement("tr");
        row.classList.add("hover:bg-gray-600", "transition-colors");
        row.setAttribute("data-filename", item.DosyaAdi);
        row.setAttribute("data-index", index.toString());


        // Dosya Adı: Tıklanabilir link
        const dosyaAdiCell = document.createElement("td");
        dosyaAdiCell.classList.add("px-4", "py-3");
        const link = document.createElement("a");
        link.href = "#";
        link.classList.add("file-link", "text-custom", "hover:underline");
        link.setAttribute("data-filename", item.DosyaAdi);
        link.textContent = item.DosyaAdi;
        dosyaAdiCell.appendChild(link);
        row.appendChild(dosyaAdiCell);

        // Oyun Adı
        const oyunAdiCell = document.createElement("td");
        oyunAdiCell.classList.add("px-4", "py-3");
        oyunAdiCell.textContent = item.OyunAdi;
        row.appendChild(oyunAdiCell);

        // Tarih
        const tarihCell = document.createElement("td");
        tarihCell.classList.add("px-4", "py-3");
        // Tarih değerini veri özniteliği olarak ekle
        tarihCell.setAttribute('data-date', item.Tarih);
        tarihCell.textContent = item.Tarih;
        row.appendChild(tarihCell);

        // Testci
        // const testciCell = document.createElement("td");
        // testciCell.classList.add("px-4", "py-3");
        // testciCell.textContent = item.Testci;
        // row.appendChild(testciCell);

        // Tip
        const tipCell = document.createElement("td");
        tipCell.classList.add("px-4", "py-3");
        tipCell.textContent = item.Tip;
        row.appendChild(tipCell);

        // Durum
        const statusCell = document.createElement("td");
        statusCell.classList.add("px-4", "py-3");

        // Durum değeri varsa özel bir stil uygula
        if (item.Status) {
            let statusBadge = document.createElement("span");

            if (item.Status === "Yüklendi" && item.DriveUrl) {
                // Yüklendi ve URL varsa, tıklanabilir buton yap
                statusBadge = document.createElement("button");
                statusBadge.classList.add("px-2", "py-1", "rounded-full", "text-sm", "bg-green-500/20", "text-green-500", "hover:bg-green-500/40", "transition-colors", "cursor-pointer");
                statusBadge.textContent = item.Status;
                statusBadge.title = "Drive'da açmak için çift tıklayın";

                // Çift tıklama olayı ekle
                statusBadge.addEventListener("dblclick", () => {
                    postMessage({
                        type: "openUrl",
                        url: item.DriveUrl
                    });
                });
            } else if (item.Status === "Yükleniyor") {
                // Yükleniyor durumu için sarı badge
                statusBadge.classList.add("px-2", "py-1", "rounded-full", "text-sm", "bg-yellow-500/20", "text-yellow-500");
                statusBadge.textContent = item.Status;
            } else if (item.Status === "Hata") {
                // Hata durumu için kırmızı badge
                statusBadge.classList.add("px-2", "py-1", "rounded-full", "text-sm", "bg-red-500/20", "text-red-500");
                statusBadge.textContent = item.Status;
            } else {
                // Diğer durumlar için gri badge
                statusBadge.classList.add("px-2", "py-1", "rounded-full", "text-sm", "bg-gray-500/20", "text-gray-500");
                statusBadge.textContent = item.Status;
            }

            statusCell.appendChild(statusBadge);
        } else {
            statusCell.textContent = "";
        }

        row.appendChild(statusCell);

        tableBody.appendChild(row);
      });
    }

    // Tablo başlıklarına tıklama eventi
    document.querySelectorAll('#videoSsTable th[data-sort]').forEach((header) => {
      header.addEventListener('click', () => {
        const column = header.getAttribute('data-sort');
        sortVideoSsTable(column);
      });
    });

    // Eğer arama kutusunda metin varsa, filtreyi yeniden uygula
    if (searchInput && searchInput.value.trim() !== '') {
      filterVideoSsTable();
    }
  } else if (event.data.type === 'paymentData') {
    paymentData = event.data.payments; // Global paymentData değişkenine ata
    refreshPaymentsTable(); // Tabloyu güncelle
  } else if (event.data.type === 'updateContinuousRecording') {
    const videoRecordLoopCheckbox = document.getElementById('videorecordloop');
    if (videoRecordLoopCheckbox) {
      const previousValue = videoRecordLoopCheckbox.checked;
      const newValue = event.data.value;

      videoRecordLoopCheckbox.checked = newValue;

      // If continuous recording was disabled and we're not currently recording,
      // stop the timer but don't reset it
      if (previousValue && !newValue) {
        const recordButton = document.querySelector('button[onclick="startVideoRecording()"]');
        const isRecording = recordButton && recordButton.textContent === 'Recording...';

        if (!isRecording) {
          videoTimerStop();
        }
      }
    }
  } else if (event.data.type === 'videoRecordingStatus') {
    const recordButton = document.querySelector('button[onclick="startVideoRecording()"]');

    if (event.data.status === 'started') {
      recordButton.classList.add('bg-red-500');
      recordButton.textContent = 'Recording...';
    } else if (event.data.status === 'stopped') {
      recordButton.classList.remove('bg-red-500');
      recordButton.disabled = false;
      recordButton.textContent = 'Video Kaydı Başlat';

      // Check if continuous recording is active
      const continuousRecording = document.getElementById('videorecordloop').checked;

      // If continuous recording is not active, stop the timer but don't reset it
      // Otherwise, keep the timer running for the next recording
      if (!continuousRecording) {
        videoTimerStop(); // Stop timer without resetting
      }
    }
  } else if (event.data.type === 'error') {
    alert(event.data.message);
    const recordButton = document.querySelector('button[onclick="startVideoRecording()"]');
    recordButton.disabled = false;
    recordButton.textContent = 'Video Kaydı Başlat';

    // Stop the video timer in case of error, but don't reset it
    videoTimerStop();
  } else if (event.data.type === 'refundStatus') {
    updateRefundStatus(event.data.paymentId, event.data.status, event.data.message);
  } else if (event.data.type === 'assigneeList') {
    const assigneeSelect = document.getElementById('taskAssignee');
    event.data.assignees.forEach(assignee => {
        const option = document.createElement('option');
        option.value = assignee.id;
        option.textContent = assignee.name;
        assigneeSelect.appendChild(option);
    });
  } else if (event.data.type === 'sprintList') {
      const sprintSelect = document.getElementById('taskSprint');
      event.data.sprints.forEach(sprint => {
          const option = document.createElement('option');
          option.value = sprint.id;
          option.textContent = sprint.name;
          sprintSelect.appendChild(option);
      });
  } else if (event.data.type === 'notification') {
    showNotification(event.data.message);
  } else if (event.data.type === 'userData') {
    loadUserDataToTable(event.data.data);
  } else if (event.data.type === 'driveFolders') {
    // Google Drive klasörlerini işle
    if (event.data.parentId === 'root') {
      // Root klasörü güncelle
      buildFolderTree(event.data.folders, 'root');
    } else {
      // Alt klasörleri güncelle
      const parentElement = document.querySelector(`.folder-children[data-folder-id="${event.data.parentId}"]`);
      if (parentElement) {
        // Yükleniyor mesajını kaldır
        parentElement.innerHTML = '';
        // Alt klasörleri ekle
        buildFolderTree(event.data.folders, event.data.parentId, parentElement);
      }
    }
  } else if (event.data.type === 'newDriveFolder') {
    // Yeni oluşturulan klasörü ekle
    const parentElement = document.querySelector(`.folder-children[data-folder-id="${event.data.parentId}"]`);
    if (parentElement) {
      // Yeni klasörü ekle
      const folderItem = document.createElement('li');
      folderItem.className = 'folder-item py-1';
      folderItem.innerHTML = `
        <div class="flex items-center px-2 py-1 rounded hover:bg-gray-600 cursor-pointer folder-name" data-folder-id="${event.data.folder.Id}">
          <i class="fas fa-folder mr-2 text-yellow-400"></i>
          <span>${event.data.folder.Name}</span>
        </div>
        <ul class="pl-6 folder-children hidden" data-folder-id="${event.data.folder.Id}"></ul>
      `;
      parentElement.appendChild(folderItem);

      // Klasöre tıklama olayı ekle
      const folderName = folderItem.querySelector('.folder-name');
      folderName.addEventListener('click', (e) => {
        selectFolder(e.currentTarget);

        // Alt klasörleri yükle
        const childrenContainer = folderItem.querySelector('.folder-children');
        if (childrenContainer.classList.contains('hidden')) {
          childrenContainer.classList.remove('hidden');

          // Eğer içi boşsa, alt klasörleri yükle
          if (childrenContainer.children.length === 0) {
            childrenContainer.innerHTML = `
              <li class="flex items-center py-1 px-2">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                <span>Yükleniyor...</span>
              </li>
            `;
            loadDriveFolders(event.data.folder.Id);
          }
        } else {
          childrenContainer.classList.add('hidden');
        }
      });

      // Eğer iç içe klasör oluşturma işlemiyse
      if (event.data.isNested) {
        // Tüm klasörler oluşturuldu mu kontrol et
        const isLastFolder = event.data.currentIndex === event.data.totalParts - 1;

        if (isLastFolder) {
          // Tüm klasörler oluşturuldu
          if (event.data.uploadAfterCreate) {
            showNotification('Dosyalar yükleniyor...', 'info');
          } else {
            showNotification('Klasörler başarıyla oluşturuldu.', 'success');
          }

          // Input'u temizle
          document.getElementById('newFolderName').value = '';
        } else if (event.data.isExistingFolder) {
          // Mevcut klasör kullanılıyor
          showNotification(`'${event.data.folder.Name}' klasörü zaten mevcut, kullanılıyor.`, 'info');
        }

        // Son oluşturulan veya mevcut klasörü seç
        selectFolder(folderName);
      } else {
        // Tek klasör oluşturma işlemi tamamlandı
        if (event.data.isExistingFolder) {
          showNotification(`'${event.data.folder.Name}' klasörü zaten mevcut, kullanılıyor.`, 'info');
        } else if (event.data.uploadAfterCreate) {
          showNotification('Dosyalar yükleniyor...', 'info');
        } else {
          showNotification('Klasör başarıyla oluşturuldu.', 'success');
        }

        // Input'u temizle
        document.getElementById('newFolderName').value = '';

        // Oluşturulan veya mevcut klasörü seç
        selectFolder(folderName);
      }
    }
  } else if (event.data.type === 'storageLocations') {
      populateStorageSelect(event.data.locations);
      
  } else if (event.data.type === 'currentFolderLocation') {
      // Store current folder location
      currentFolderLocation = event.data.location;

      // Update the select element if it exists
      if (storageSelect && storageSelect.options.length > 0) {
          for (let i = 0; i < storageSelect.options.length; i++) {
              if (storageSelect.options[i].value === currentFolderLocation) {
                  storageSelect.selectedIndex = i;
                  break;
              }
          }
      }
  } else if (event.data.type === 'storageLocationSaved') {
      
  }
});