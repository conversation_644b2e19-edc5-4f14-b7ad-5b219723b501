/**
 * <PERSON><PERSON><PERSON> menüsünün konumunu ekranın sınırları içinde kalacak şekilde ayarlar
 * Menü ekran dışına taşacaksa otomatik olarak ters yöne açılır
 * @param {HTMLElement} contextMenu - Bağlam menüsü elementi
 * @param {number} x - Fare tıklama X koordinatı (clientX)
 * @param {number} y - Fare tıklama Y koordinatı (clientY)
 */
function positionContextMenu(contextMenu, x, y) {
    if (!contextMenu) return;

    // Önce menüyü görünür hale getir ama pozisyonunu ayarlama
    // Bu, boyutlarını doğru şekilde alabilmemiz için gerekli
    contextMenu.style.visibility = 'hidden';
    contextMenu.style.position = 'fixed'; // fixed kullanarak viewport koordinatlarıyla çalış
    contextMenu.classList.remove('hidden');

    // Menünün boyutlarını al
    const menuWidth = contextMenu.offsetWidth;
    const menuHeight = contextMenu.offsetHeight;

    // Viewport boyutlarını al
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    let finalX = x;
    let finalY = y;

    // Yatay pozisyon kontrolü (sağa/sola)
    if (x + menuWidth > windowWidth) {
        // Sağa taşıyor, sola aç
        finalX = x - menuWidth;

        // Eğer sola açınca da taşıyorsa, viewport içinde en uygun yere yerleştir
        if (finalX < 0) {
            finalX = Math.max(5, windowWidth - menuWidth - 5);
        }
    }

    // Dikey pozisyon kontrolü (yukarı/aşağı)
    if (y + menuHeight > windowHeight) {
        // Aşağıya taşıyor, yukarı aç
        finalY = y - menuHeight;

        // Eğer yukarı açınca da taşıyorsa, viewport içinde en uygun yere yerleştir
        if (finalY < 0) {
            finalY = Math.max(5, windowHeight - menuHeight - 5);
        }
    }

    // Güvenlik kontrolü - menü hala viewport dışındaysa zorla içeri al
    finalX = Math.max(5, Math.min(finalX, windowWidth - menuWidth - 5));
    finalY = Math.max(5, Math.min(finalY, windowHeight - menuHeight - 5));

    // Menünün konumunu ayarla
    contextMenu.style.left = finalX + 'px';
    contextMenu.style.top = finalY + 'px';
    contextMenu.style.visibility = 'visible';
}
