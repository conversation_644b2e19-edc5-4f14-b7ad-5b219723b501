  /* ===== GLOBAL INPUT OVERRIDE ===== */
  * {
    box-sizing: border-box;
  }

  input, textarea, select {
    background-color: var(--color-surface) !important;
    color: var(--color-text-primary) !important;
    border: 1px solid var(--color-border) !important;
  }

  /* ===== MODERN TAB SYSTEM ===== */
  .tab-button {
    position: relative;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--color-text-muted);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-radius: var(--radius-md);
    white-space: nowrap;
  }

  .tab-button:hover {
    background: var(--color-surface);
    color: var(--color-text-secondary);
  }

  .tab-active {
    background: var(--color-primary) !important;
    color: white !important;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .tab-active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: white;
    border-radius: var(--radius-sm);
  }

  .tab-inactive {
    background: transparent;
    color: var(--color-text-muted);
  }

  /* Navigation Container */
  nav ul {
    display: flex;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: var(--color-surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border);
  }


  /* ===== MODERN DESIGN SYSTEM ===== */
  :root {
    /* Color System */
    --color-custom-rgb: 37, 99, 235; /* Modern mavi */
    --color-custom: rgb(var(--color-custom-rgb));
    --color-custom-hover: rgb(29, 78, 216);
    --color-custom-light: rgb(59, 130, 246);

    /* Semantic Colors */
    --color-primary: #2563EB;
    --color-primary-hover: #1D4ED8;
    --color-secondary: #10B981;
    --color-secondary-hover: #059669;
    --color-accent: #8B5CF6;
    --color-accent-hover: #7C3AED;

    /* Status Colors */
    --color-success: #10B981;
    --color-warning: #F59E0B;
    --color-error: #EF4444;
    --color-info: #06B6D4;

    /* Neutral Colors - Enhanced Dark Theme */
    --color-background: #0F172A;
    --color-background-secondary: #1E293B;
    --color-surface: #1E293B;
    --color-surface-hover: #334155;
    --color-surface-light: #475569;
    --color-surface-elevated: #2D3748;
    --color-border: #334155;
    --color-border-light: #475569;
    --color-border-subtle: #2D3748;

    /* Text Colors */
    --color-text-primary: #F8FAFC;
    --color-text-secondary: #CBD5E1;
    --color-text-muted: #94A3B8;
    --color-text-disabled: #64748B;

    /* Spacing System */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
  }

  /* ===== GLOBAL STYLES ===== */
  * {
    box-sizing: border-box;
  }

  body {
    font-family: var(--font-family-primary);
    background: var(--color-background);
    color: var(--color-text-primary);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    font-size: var(--font-size-sm);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* ===== ENHANCED DARK THEME SYSTEM ===== */

  /* Dynamic Background Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-hover) 100%);
  }

  .gradient-surface {
    background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-hover) 100%);
  }

  .gradient-background {
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-background-secondary) 50%, #1E293B 100%);
  }

  /* Glassmorphism Effects */
  .glass {
    background: rgba(30, 41, 59, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-light {
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  /* Enhanced Shadows for Dark Theme */
  :root {
    --shadow-colored: 0 4px 6px -1px rgba(37, 99, 235, 0.1), 0 2px 4px -2px rgba(37, 99, 235, 0.1);
    --shadow-colored-lg: 0 10px 15px -3px rgba(37, 99, 235, 0.1), 0 4px 6px -4px rgba(37, 99, 235, 0.1);
  }

  /* ===== LAYOUT SYSTEM ===== */
  .main-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    background: var(--color-background);
    background-image:
      radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    overflow: hidden;
  }

  .content-area {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .scrollable-content {
    height: calc(100vh - 165px);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--color-surface-light) transparent;
  }

  .scrollable-content::-webkit-scrollbar {
    width: 6px;
  }

  .scrollable-content::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollable-content::-webkit-scrollbar-thumb {
    background: var(--color-surface-light);
    border-radius: var(--radius-lg);
  }

  .scrollable-content::-webkit-scrollbar-thumb:hover {
    background: var(--color-border-light);
  }

  /* ===== ENHANCED MODERN CARD SYSTEM ===== */
  .card {
    background: var(--color-surface);
    border: 1px solid var(--color-border-subtle);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    overflow: hidden;
    position: relative;
    min-height: 200px;
    height: fit-content;
    min-width: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity var(--transition-fast);
  }

  .card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--color-border-light);
    transform: translateY(-2px);
  }

  .card:hover::before {
    opacity: 1;
  }

  .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface-hover);
  }

  .card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
  }

  .card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-muted);
    margin: var(--spacing-xs) 0 0 0;
  }

  .card-body {
    padding: var(--spacing-lg);
  }

  .card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
    background: var(--color-surface-hover);
  }

  /* Glass Card Variant */
  .card-glass {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Elevated Card Variant */
  .card-elevated {
    background: var(--color-surface-elevated);
    box-shadow: var(--shadow-lg), var(--shadow-colored);
    border: 1px solid var(--color-border-light);
    position: relative;
  }

  .card-elevated::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: var(--radius-lg);
    padding: 1px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent, rgba(255, 255, 255, 0.05));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    pointer-events: none;
    opacity: 0;
    transition: opacity var(--transition-fast);
  }

  .card-elevated:hover {
    box-shadow: var(--shadow-xl), var(--shadow-colored-lg);
    transform: translateY(-4px);
  }

  .card-elevated:hover::after {
    opacity: 1;
  }

  /* Layout Components */
  footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: var(--color-surface);
    border-top: 1px solid var(--color-border);
  }

  .action-buttons {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    border: 1px solid var(--color-border);
    z-index: var(--z-sticky);
  }

  /* ===== MODERN BUTTON SYSTEM ===== */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    user-select: none;
    white-space: nowrap;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.2);
  }

  /* Button Variants */
  .btn-primary {
    background: var(--color-primary);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-primary:hover {
    background: var(--color-primary-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .btn-secondary {
    background: var(--color-secondary);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-secondary:hover {
    background: var(--color-secondary-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .btn-success {
    background: var(--color-success);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-success:hover {
    background: #059669;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .btn-danger {
    background: var(--color-error);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-danger:hover {
    background: #DC2626;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .btn-warning {
    background: var(--color-warning);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-warning:hover {
    background: #D97706;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .btn-info {
    background: var(--color-info);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-info:hover {
    background: #0891B2;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  /* Button Sizes */
  .btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }

  .btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
  }

  /* Button Styles */
  .btn-outline {
    background: transparent;
    border: 1px solid var(--color-border);
    color: var(--color-text-secondary);
  }

  .btn-outline:hover {
    background: var(--color-surface);
    border-color: var(--color-border-light);
    color: var(--color-text-primary);
  }

  .btn-ghost {
    background: transparent;
    color: var(--color-text-secondary);
    box-shadow: none;
  }

  .btn-ghost:hover {
    background: var(--color-surface);
    color: var(--color-text-primary);
    transform: none;
  }

  /* ===== MODERN FORM SYSTEM ===== */
  .form-input,
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  textarea,
  select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--color-surface) !important;
    border: 1px solid var(--color-border) !important;
    border-radius: var(--radius-md);
    color: var(--color-text-primary) !important;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    transition: all var(--transition-fast);
    -webkit-appearance: none;
    appearance: none;
  }

  .form-input:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: none !important;
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.1) !important;
    background: var(--color-surface-hover) !important;
  }

  .form-input::placeholder,
  input::placeholder,
  textarea::placeholder {
    color: var(--color-text-disabled) !important;
    opacity: 0.7;
  }

  /* Select Styling */
  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-sm) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
  }

  /* Autofill ve browser default override */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  textarea:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px var(--color-surface) inset !important;
    -webkit-text-fill-color: var(--color-text-primary) !important;
    background-color: var(--color-surface) !important;
    background: var(--color-surface) !important;
  }

  /* Tüm input türleri için güçlü override */
  input,
  textarea,
  select {
    background-color: var(--color-surface) !important;
    background: var(--color-surface) !important;
    color: var(--color-text-primary) !important;
  }

  /* Modern Checkbox Styling */
  .form-checkbox,
  input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    background: var(--color-surface-hover);
    border: 2px solid var(--color-border);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    outline: none;
    vertical-align: middle;
    margin: 0;
    flex-shrink: 0;
    margin: unset !important;
    padding: unset !important;
    min-height: unset !important;
  }

  .form-checkbox:hover,
  input[type="checkbox"]:hover {
    border-color: var(--color-primary);
    background: var(--color-surface-light);
    box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.1);
  }

  .form-checkbox:checked,
  input[type="checkbox"]:checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.2);
  }

  .form-checkbox:checked::before,
  input[type="checkbox"]:checked::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: translate(-50%, -60%) rotate(45deg);
    opacity: 1;
  }

  .form-checkbox:focus,
  input[type="checkbox"]:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.2);
  }

  .form-checkbox:disabled,
  input[type="checkbox"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--color-surface);
    border-color: var(--color-border-subtle);
  }

  .form-checkbox:disabled:checked,
  input[type="checkbox"]:disabled:checked {
    background: var(--color-text-disabled);
    border-color: var(--color-text-disabled);
  }

  /* Checkbox Label Alignment */
  .checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
  }

  .checkbox-container label {
    cursor: pointer;
    user-select: none;
    margin: 0;
  }

  /* Table Checkbox Styling */
  table .form-checkbox,
  table input[type="checkbox"] {
    margin: 0 auto;
    display: block;
  }

  /* Header Checkbox for Select All */
  th .form-checkbox,
  th input[type="checkbox"] {
    margin: 0;
  }

  /* Label Styling */
  label {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
  }

  /* Notification Banner */
  #notification-banner {
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
    color: white;
    padding: var(--spacing-md);
    text-align: center;
    font-weight: 600;
    box-shadow: var(--shadow-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
  }

  /* ===== MODERN TABLE SYSTEM ===== */
  .table-container {
    background: var(--color-surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-border);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
  }

  thead {
    background: var(--color-surface-hover);
    border-bottom: 1px solid var(--color-border);
  }

  th {
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--color-text-primary);
    font-size: var(--font-size-sm);
    border-bottom: 1px solid var(--color-border);
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
  }

  th:hover {
    background: var(--color-surface-light);
    color: var(--color-primary);
  }

  td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text-secondary);
    transition: all var(--transition-fast);
  }

  tbody tr {
    transition: all var(--transition-fast);
    cursor: pointer;
  }

  tbody tr:hover {
    background: var(--color-surface-hover);
  }

  tbody tr:last-child td {
    border-bottom: none;
  }

  /* Table Row States */
  .table-row-selected {
    background: rgba(var(--color-custom-rgb), 0.1) !important;
    border-left: 3px solid var(--color-primary);
  }

  .table-row-active {
    background: var(--color-surface-light);
  }

  /* Responsive Table */
  .table-responsive {
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--color-surface-light) transparent;
  }

  .table-responsive::-webkit-scrollbar {
    height: 6px;
  }

  .table-responsive::-webkit-scrollbar-track {
    background: transparent;
  }

  .table-responsive::-webkit-scrollbar-thumb {
    background: var(--color-surface-light);
    border-radius: var(--radius-lg);
  }

  /* ===== MODERN MODAL SYSTEM ===== */
  .modal-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal-backdrop);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
  }

  .modal {
    background: var(--color-surface);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--color-border);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.2s ease-out;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
  }

  .modal-close {
    background: transparent;
    border: none;
    color: var(--color-text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
  }

  .modal-close:hover {
    background: var(--color-surface-hover);
    color: var(--color-text-primary);
  }

  .modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
  }

  .modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
  }

  /* ===== MODERN CONTEXT MENU SYSTEM ===== */
  .context-menu {
    position: absolute;
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-popover);
    min-width: 180px;
    overflow: hidden;
    animation: contextMenuSlideIn 0.15s ease-out;
  }

  @keyframes contextMenuSlideIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-5px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .context-menu ul {
    list-style: none;
    margin: 0;
    padding: var(--spacing-xs);
  }

  .context-menu li {
    margin: 0;
    padding: 0;
    white-space: nowrap;
  }

  .context-menu li > * {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--color-text-secondary);
    text-decoration: none;
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    white-space: nowrap;
  }

  /* Video SS context menu için özel stil */
  .context-menu li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--color-text-secondary);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    white-space: nowrap;
  }

  .context-menu li:not(.separator):hover {
    background: var(--color-surface-hover);
    color: var(--color-text-primary);
  }

  .context-menu li:hover > * {
    background: var(--color-surface-hover);
    color: var(--color-text-primary);
  }

  .context-menu .separator {
    height: 1px;
    background: var(--color-border);
    margin: var(--spacing-xs) 0;
  }

  /* ===== UTILITY CLASSES ===== */

  /* Spacing Utilities */
  .p-0 { padding: 0; }
  .p-1 { padding: var(--spacing-xs); }
  .p-2 { padding: var(--spacing-sm); }
  .p-3 { padding: var(--spacing-md); }
  .p-4 { padding: var(--spacing-lg); }
  .p-5 { padding: var(--spacing-xl); }

  .m-0 { margin: 0; }
  .m-1 { margin: var(--spacing-xs); }
  .m-2 { margin: var(--spacing-sm); }
  .m-3 { margin: var(--spacing-md); }
  .m-4 { margin: var(--spacing-lg); }
  .m-5 { margin: var(--spacing-xl); }

  .mb-1 { margin-bottom: var(--spacing-xs); }
  .mb-2 { margin-bottom: var(--spacing-sm); }
  .mb-3 { margin-bottom: var(--spacing-md); }
  .mb-4 { margin-bottom: var(--spacing-lg); }

  .mt-1 { margin-top: var(--spacing-xs); }
  .mt-2 { margin-top: var(--spacing-sm); }
  .mt-3 { margin-top: var(--spacing-md); }
  .mt-4 { margin-top: var(--spacing-lg); }

  /* Display Utilities */
  .hidden { display: none !important; }
  .block { display: block; }
  .inline-block { display: inline-block; }
  .flex { display: flex; }
  .inline-flex { display: inline-flex; }
  .grid {
    display: grid;
    min-height: 0;
    align-content: start;
  }

  /* Flexbox Utilities */
  .flex-col { flex-direction: column; }
  .flex-row { flex-direction: row; }
  .items-center { align-items: center; }
  .items-start { align-items: flex-start; }
  .items-end { align-items: flex-end; }
  .justify-center { justify-content: center; }
  .justify-between { justify-content: space-between; }
  .justify-end { justify-content: flex-end; }
  .flex-1 { flex: 1; }
  .flex-wrap { flex-wrap: wrap; }

  /* Text Utilities */
  .text-left { text-align: left; }
  .text-center { text-align: center; }
  .text-right { text-align: right; }
  .text-xs { font-size: var(--font-size-xs); }
  .text-sm { font-size: var(--font-size-sm); }
  .text-base { font-size: var(--font-size-base); }
  .text-lg { font-size: var(--font-size-lg); }
  .text-xl { font-size: var(--font-size-xl); }
  .text-2xl { font-size: var(--font-size-2xl); }
  .font-normal { font-weight: 400; }
  .font-medium { font-weight: 500; }
  .font-semibold { font-weight: 600; }
  .font-bold { font-weight: 700; }

  /* Color Utilities */
  .text-primary { color: var(--color-text-primary); }
  .text-secondary { color: var(--color-text-secondary); }
  .text-muted { color: var(--color-text-muted); }
  .text-success { color: var(--color-success); }
  .text-warning { color: var(--color-warning); }
  .text-error { color: var(--color-error); }

  /* Background Utilities */
  .bg-surface { background: var(--color-surface); }
  .bg-surface-hover { background: var(--color-surface-hover); }
  .bg-primary { background: var(--color-primary); }
  .bg-success { background: var(--color-success); }
  .bg-warning { background: var(--color-warning); }
  .bg-error { background: var(--color-error); }

  /* Border Utilities */
  .border { border: 1px solid var(--color-border); }
  .border-t { border-top: 1px solid var(--color-border); }
  .border-b { border-bottom: 1px solid var(--color-border); }
  .border-l { border-left: 1px solid var(--color-border); }
  .border-r { border-right: 1px solid var(--color-border); }
  .rounded { border-radius: var(--radius-md); }
  .rounded-lg { border-radius: var(--radius-lg); }
  .rounded-xl { border-radius: var(--radius-xl); }

  /* Shadow Utilities */
  .shadow-sm { box-shadow: var(--shadow-sm); }
  .shadow-md { box-shadow: var(--shadow-md); }
  .shadow-lg { box-shadow: var(--shadow-lg); }
  .shadow-xl { box-shadow: var(--shadow-xl); }

  /* Width/Height Utilities */
  .w-full { width: 100%; }
  .h-full { height: 100%; }
  .w-auto { width: auto; }
  .h-auto { height: auto; }

  /* Position Utilities */
  .relative { position: relative; }
  .absolute { position: absolute; }
  .fixed { position: fixed; }
  .sticky { position: sticky; }

  /* Overflow Utilities */
  .overflow-hidden { overflow: hidden; }
  .overflow-auto { overflow: auto; }
  .overflow-x-auto { overflow-x: auto; }
  .overflow-y-auto { overflow-y: auto; }

  /* Transition Utilities */
  .transition { transition: all var(--transition-fast); }
  .transition-colors { transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast); }

  /* Legacy Support */
  .select-padding {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  /* ===== STATUS INDICATORS & BADGES ===== */
  .badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
  }

  .badge-primary {
    background: rgba(var(--color-custom-rgb), 0.1);
    color: var(--color-primary);
    border: 1px solid rgba(var(--color-custom-rgb), 0.2);
  }

  .badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--color-success);
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  .badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  .badge-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--color-error);
    border: 1px solid rgba(239, 68, 68, 0.2);
  }

  .badge-info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--color-info);
    border: 1px solid rgba(6, 182, 212, 0.2);
  }

  /* Status Dots */
  .status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--spacing-xs);
  }

  .status-dot-online {
    background: var(--color-success);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }

  .status-dot-offline {
    background: var(--color-error);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }

  .status-dot-warning {
    background: var(--color-warning);
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }

  /* Progress Indicators */
  .progress-bar {
    width: 100%;
    height: 4px;
    background: var(--color-surface-hover);
    border-radius: var(--radius-lg);
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-hover));
    border-radius: var(--radius-lg);
    transition: width var(--transition-normal);
  }

  /* Loading Spinner */
  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-surface-hover);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Pulse Animation for Live Elements */
  .pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  /* ===== MICRO-ANIMATIONS & ENHANCED INTERACTIONS ===== */

  /* Smooth Scale Animations */
  .scale-on-hover {
    transition: transform var(--transition-fast);
  }

  .scale-on-hover:hover {
    transform: scale(1.02);
  }

  .scale-on-click:active {
    transform: scale(0.98);
  }

  /* Glow Effects */
  .glow-on-hover {
    transition: box-shadow var(--transition-fast);
  }

  .glow-on-hover:hover {
    box-shadow: 0 0 20px rgba(var(--color-custom-rgb), 0.3);
  }

  /* Slide Animations */
  .slide-in-from-left {
    animation: slideInFromLeft 0.3s ease-out;
  }

  .slide-in-from-right {
    animation: slideInFromRight 0.3s ease-out;
  }

  .slide-in-from-top {
    animation: slideInFromTop 0.3s ease-out;
  }

  .slide-in-from-bottom {
    animation: slideInFromBottom 0.3s ease-out;
  }

  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromBottom {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Fade Animations */
  .fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .fade-in-delayed {
    animation: fadeIn 0.5s ease-out 0.1s both;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Bounce Animation for Success States */
  .bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Shake Animation for Error States */
  .shake {
    animation: shake 0.5s ease-in-out;
  }

  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(5px);
    }
  }

  /* Enhanced Button Interactions */
  .btn {
    position: relative;
    overflow: hidden;
  }

  .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  .btn:hover::before {
    width: 300px;
    height: 300px;
  }

  /* Enhanced Tab Animations */
  .tab-button {
    position: relative;
    overflow: hidden;
  }

  .tab-button::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--color-primary);
    transition: width var(--transition-fast);
  }

  .tab-button:hover::before {
    width: 100%;
  }

  .tab-active::before {
    width: 100% !important;
  }

  /* Stagger Animation for Lists */
  .stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: staggerFadeIn 0.4s ease-out forwards;
  }

  .stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
  .stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
  .stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
  .stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
  .stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }

  @keyframes staggerFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* ===== COMPACT DESIGN - ESKİ SÜRÜM GİBİ ===== */
  /* Kompakt tasarım - eski sürüm gibi tüm ekranlar için */
  .main-container {
    padding: 4px !important;
  }

  .grid {
    /* Default 4 sütun - JavaScript ile dinamik olarak değiştirilecek */
    grid-template-columns: 1.5fr 1.6fr 2.6fr 1.5fr !important;
    gap: 4px !important;
  }

  /* Admin olmayan kullanıcılar için 3 sütun layout */
  .grid.non-admin {
    grid-template-columns: 1.5fr 2fr 3fr !important;
  }

  /* Tab Navigation Responsive */
  nav {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }

  nav::-webkit-scrollbar {
    height: 4px;
  }

  nav::-webkit-scrollbar-track {
    background: transparent;
  }

  nav::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }

  /* QA Task Table Responsive */
  #qaTaskTable {
    font-size: 0.75rem;
  }

  #qaTaskTable th,
  #qaTaskTable td {
    padding: 0.5rem 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  #qaTaskTable th {
    position: sticky;
    top: 0;
    background: var(--card-bg);
    z-index: 10;
  }

  /* Responsive table container */
  .table-responsive {
    position: relative;
  }

  .table-responsive::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  .table-responsive::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  .table-responsive::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }

  .table-responsive::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  /* Device Controls özel grid - her zaman 2 sütun */
  .device-controls-grid {
    grid-template-columns: repeat(1, 1fr) !important;
    gap: 4px !important;
  }

  .card {
    min-height: auto !important;
    height: auto !important;
  }

  .card-body {
    padding: 8px !important;
  }

  .card-header {
    padding: 8px !important;
    display: flex;
    justify-content: center;
  }

  .card-title {
    font-size: 13px !important;
    font-weight: 600;
    margin-bottom: 4px !important;
  }

  .btn {
    padding: 6px 12px !important;
    font-size: 11px !important;
    min-height: 32px !important;
    margin: 2px !important;
  }

  input, textarea, select {
    font-size: 11px !important;
    padding: 6px 8px !important;
    min-height: 30px !important;
    margin: 2px 0 !important;
  }

  .fas, .far {
    font-size: 11px !important;
  }

  label {
    font-size: 11px !important;
    margin-bottom: 2px !important;
  }

  .timer-display {
    font-size: 14px !important;
    padding: 6px 8px !important;
  }

  .device-info-grid {
    gap: 4px !important;
    font-size: 11px !important;
  }


  /* Print Styles */
  @media print {
    .btn,
    .tab-button,
    nav {
      display: none !important;
    }

    .card {
      box-shadow: none;
      border: 1px solid #000;
    }

    body {
      background: white !important;
      color: black !important;
    }
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    :root {
      --color-border: #ffffff;
      --color-text-secondary: #ffffff;
      --color-text-muted: #cccccc;
    }
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .btn::before,
    .tab-button::before,
    .card::before {
      display: none;
    }
  }

  /* ===== SPECIALIZED COMPONENTS ===== */

  /* Video SS Table Specific Styles */
  #videoSsTable {
    font-size: 0.875rem;
  }

  #videoSsTable thead {
    background: linear-gradient(135deg, var(--color-surface-hover), var(--color-surface-light));
    border-bottom: 2px solid var(--color-border);
  }

  #videoSsTable th {
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--color-text-primary);
    background: linear-gradient(135deg, var(--color-surface-hover), var(--color-surface-light));
    border-right: 1px solid var(--color-border);
    position: relative;
    white-space: nowrap;
  }

  #videoSsTable th:last-child {
    border-right: none;
  }

  #videoSsTable th:hover {
    background: linear-gradient(135deg, var(--color-surface-light), var(--color-surface-hover));
    color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  #videoSsTable th i.fas {
    font-size: 0.75rem;
    opacity: 0.8;
  }

  #videoSsTable th i.fa-sort,
  #videoSsTable th i.fa-sort-up,
  #videoSsTable th i.fa-sort-down {
    transition: all 0.2s ease;
  }

  #videoSsTable th:hover i.fa-sort {
    opacity: 1;
    color: var(--color-primary);
  }

  #videoSsTable td {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.4;
    border-right: 1px solid var(--color-border);
  }

  #videoSsTable td:last-child {
    border-right: none;
  }

  #videoSsTable tbody tr {
    height: 2.5rem;
    transition: all 0.2s ease;
  }

  #videoSsTable tbody tr:hover {
    background: var(--color-surface-hover);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  #videoSsTable tbody tr.selected {
    background: rgba(var(--color-custom-rgb), 0.15) !important;
    border-left: 3px solid var(--color-primary);
  }

  #videoSsTable tbody tr.selected:hover {
    background: rgba(var(--color-custom-rgb), 0.2) !important;
  }

  /* Video SS Table Responsive */
  @media (max-width: 1024px) {
    #videoSsTable th,
    #videoSsTable td {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
    }

    #videoSsTable th {
      font-size: 0.75rem;
    }

    #videoSsTable tbody tr {
      height: 2.25rem;
    }
  }

  @media (max-width: 768px) {
    #videoSsTable th,
    #videoSsTable td {
      padding: 0.4rem 0.5rem;
      font-size: 0.75rem;
    }

    #videoSsTable th {
      font-size: 0.7rem;
    }

    #videoSsTable tbody tr {
      height: 2rem;
    }

    #videoSsTable th .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }

    #videoSsTable th .flex .flex {
      flex-direction: row;
      align-items: center;
    }
  }

  /* Payments Table Specific Styles */
  #paymentsTable {
    font-size: 0.875rem;
  }

  #paymentsTable thead {
    background: linear-gradient(135deg, var(--color-surface-hover), var(--color-surface-light));
    border-bottom: 2px solid var(--color-border);
  }

  #paymentsTable th {
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--color-text-primary);
    background: linear-gradient(135deg, var(--color-surface-hover), var(--color-surface-light));
    border-right: 1px solid var(--color-border);
    position: relative;
    white-space: nowrap;
  }

  #paymentsTable th:last-child {
    border-right: none;
  }

  #paymentsTable th:hover {
    background: linear-gradient(135deg, var(--color-surface-light), var(--color-surface-hover));
    color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  #paymentsTable th i.fas {
    font-size: 0.75rem;
    opacity: 0.8;
  }

  #paymentsTable th i.fa-sort,
  #paymentsTable th i.fa-sort-up,
  #paymentsTable th i.fa-sort-down {
    transition: all 0.2s ease;
  }

  #paymentsTable th:hover i.fa-sort {
    opacity: 1;
    color: var(--color-primary);
  }

  #paymentsTable td {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.4;
    border-right: 1px solid var(--color-border);
  }

  #paymentsTable td:last-child {
    border-right: none;
  }

  #paymentsTable tbody tr {
    height: 2.5rem;
    transition: all 0.2s ease;
  }

  #paymentsTable tbody tr:hover {
    background: var(--color-surface-hover);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  #paymentsTable tbody tr.selected {
    background: rgba(var(--color-custom-rgb), 0.15) !important;
    border-left: 3px solid var(--color-primary);
  }

  #paymentsTable tbody tr.selected:hover {
    background: rgba(var(--color-custom-rgb), 0.2) !important;
  }

  /* Payments Table Responsive */
  @media (max-width: 1024px) {
    #paymentsTable th,
    #paymentsTable td {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
    }

    #paymentsTable th {
      font-size: 0.75rem;
    }

    #paymentsTable tbody tr {
      height: 2.25rem;
    }
  }

  @media (max-width: 768px) {
    #paymentsTable th,
    #paymentsTable td {
      padding: 0.4rem 0.5rem;
      font-size: 0.75rem;
    }

    #paymentsTable th {
      font-size: 0.7rem;
    }

    #paymentsTable tbody tr {
      height: 2rem;
    }

    #paymentsTable th .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }

    #paymentsTable th .flex .flex {
      flex-direction: row;
      align-items: center;
    }
  }

  /* Local Logs Table Specific Styles */
  #localLogsTable {
    font-size: 0.875rem;
  }

  #localLogsTable thead {
    background: linear-gradient(135deg, var(--color-surface-hover), var(--color-surface-light));
    border-bottom: 2px solid var(--color-border);
  }

  #localLogsTable th {
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--color-text-primary);
    background: linear-gradient(135deg, var(--color-surface-hover), var(--color-surface-light));
    border-right: 1px solid var(--color-border);
    position: relative;
    white-space: nowrap;
  }

  #localLogsTable th:last-child {
    border-right: none;
  }

  #localLogsTable th:hover {
    background: linear-gradient(135deg, var(--color-surface-light), var(--color-surface-hover));
    color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  #localLogsTable th i.fas {
    font-size: 0.75rem;
    opacity: 0.8;
  }

  #localLogsTable th i.fa-sort,
  #localLogsTable th i.fa-sort-up,
  #localLogsTable th i.fa-sort-down {
    transition: all 0.2s ease;
  }

  #localLogsTable th:hover i.fa-sort {
    opacity: 1;
    color: var(--color-primary);
  }

  #localLogsTable td {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.4;
    border-right: 1px solid var(--color-border);
  }

  #localLogsTable td:last-child {
    border-right: none;
  }

  #localLogsTable tbody tr {
    height: 2.5rem;
    transition: all 0.2s ease;
  }

  #localLogsTable tbody tr:hover {
    background: var(--color-surface-hover);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  #localLogsTable tbody tr.selected,
  #localLogsTable tbody tr.bg-blue-600\/20 {
    background: rgba(var(--color-custom-rgb), 0.15) !important;
    border-left: 3px solid var(--color-primary);
  }

  #localLogsTable tbody tr.selected:hover,
  #localLogsTable tbody tr.bg-blue-600\/20:hover {
    background: rgba(var(--color-custom-rgb), 0.2) !important;
  }

  /* Local Logs Table Responsive */
  @media (max-width: 1024px) {
    #localLogsTable th,
    #localLogsTable td {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
    }

    #localLogsTable th {
      font-size: 0.75rem;
    }

    #localLogsTable tbody tr {
      height: 2.25rem;
    }
  }

  @media (max-width: 768px) {
    #localLogsTable th,
    #localLogsTable td {
      padding: 0.4rem 0.5rem;
      font-size: 0.75rem;
    }

    #localLogsTable th {
      font-size: 0.7rem;
    }

    #localLogsTable tbody tr {
      height: 2rem;
    }

    #localLogsTable th .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }

    #localLogsTable th .flex .flex {
      flex-direction: row;
      align-items: center;
    }
  }

  /* Timer Display Component */
  .timer-display {
    background: var(--color-surface-hover);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    text-align: center;
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-xl);
    font-weight: bold;
    color: var(--color-primary);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
  }

  .timer-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  /* Device Info Grid Component */
  .device-info-grid {
    display: grid;
    gap: var(--spacing-xs);
    background: var(--color-surface-hover);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
  }

  .device-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--color-border-subtle);
  }

  .device-info-item:last-child {
    border-bottom: none;
  }

  .device-info-label {
    color: var(--color-text-muted);
    font-size: var(--font-size-xs);
    font-weight: 500;
  }

  .device-info-value {
    color: var(--color-text-primary);
    font-size: var(--font-size-xs);
    font-weight: 600;
    font-family: monospace;
  }

  /* Action Button Group Component */
  .action-button-group {
    display: flex;
    margin: unset !important;
  }

  .action-button-group .btn {
    flex: 1;
    margin: 0;
  }

  /* Status Indicator Component */
  .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid;
    position: relative;
    overflow: hidden;
  }

  .status-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .status-indicator:hover::before {
    left: 100%;
  }

  .status-connected {
    background: rgba(16, 185, 129, 0.1);
    color: var(--color-success);
    border-color: var(--color-success);
  }

  .status-disconnected {
    background: rgba(239, 68, 68, 0.1);
    color: var(--color-error);
    border-color: var(--color-error);
  }

  .status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-warning);
    border-color: var(--color-warning);
  }

  /* Search Input Component */
  .search-input-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-input-container .search-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--color-text-muted);
    pointer-events: none;
    z-index: 1;
  }

  .search-input-container input {
    padding-left: 2.5rem;
  }

  .search-input-container .clear-button {
    position: absolute;
    right: var(--spacing-md);
    background: none;
    border: none;
    color: var(--color-text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
  }

  .search-input-container .clear-button:hover {
    background: var(--color-surface-hover);
    color: var(--color-text-primary);
  }

  /* ===== PERFORMANCE OPTIMIZATIONS ===== */

  /* GPU Acceleration for Smooth Animations */
  .card,
  .btn,
  .tab-button,
  .modal,
  .context-menu {
    will-change: transform;
    transform: translateZ(0);
  }

  /* Optimize Repaints */
  .timer-display::before,
  .btn::before,
  .card::before {
    will-change: left, opacity;
  }

  /* Reduce Layout Thrashing */
  .scrollable-content {
    contain: layout style paint;
  }

  .card-body {
    contain: layout style;
  }

  /* Optimize Font Rendering */
  body {
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: "liga", "kern";
    font-feature-settings: "liga", "kern";
  }

  /* Smooth Scrolling */
  .scrollable-content {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Reduce Motion for Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .timer-display::before,
    .btn::before,
    .card::before,
    .status-indicator::before {
      animation: none !important;
      transition: none !important;
    }

    .card:hover,
    .btn:hover {
      transform: none !important;
    }
  }

  /* High DPI Display Optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .timer-display,
    .device-info-grid,
    .card {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* Dark Mode System Preference Support */
  @media (prefers-color-scheme: dark) {
    :root {
      color-scheme: dark;
    }
  }

  /* Focus Management for Accessibility */
  .btn:focus-visible,
  .tab-button:focus-visible,
  .form-input:focus-visible,
  select:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  /* Rounded Button Support */
  .rounded-button,
  .\!rounded-button {
    border-radius: var(--radius-md) !important;
  }

  /* ===== BROWSER COMPATIBILITY ===== */

  /* Safari Specific Fixes */
  @supports (-webkit-backdrop-filter: blur(10px)) {
    .glass,
    .glass-light {
      -webkit-backdrop-filter: blur(10px);
    }
  }

  /* Firefox Specific Fixes */
  @-moz-document url-prefix() {
    .scrollable-content {
      scrollbar-width: thin;
      scrollbar-color: var(--color-surface-light) transparent;
    }
  }

  /* Edge/IE Fallbacks */
  @supports not (backdrop-filter: blur(10px)) {
    .glass,
    .glass-light {
      background: var(--color-surface);
    }
  }

/* Login ekranı için stil tanımlamaları - GPU optimize edilmiş */
#login {
  background: linear-gradient(to right bottom, #111827, #1f2937);
  position: relative;
  overflow: hidden;
}

/* Animasyonları kaldırdık ve arka plan resmini statik hale getirdik */

#login .mx-auto {
  background: rgba(17, 24, 39, 0.7);
  /* backdrop-filter kaldırıldı - GPU yoğun */
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* transform ve transition kaldırıldı */
}

/* Hover efektini basitleştirdik */
#login .mx-auto:hover {
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.4);
}

#login input {
  /* Transition süresini azalttık */
  transition: border-color 0.2s ease;
}

#login input:focus {
  /* Transform kaldırıldı, sadece border rengi değişiyor */
  border-color: var(--color-custom);
}

#login button {
  /* Transition süresini azalttık */
  transition: background-color 0.2s ease;
}

/* Buton parlama efektini kaldırdık */

/* Task Creator için stil tanımlamaları */
#studioqa {
  min-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* Profil sayfası için stil tanımlamaları */
#profile {
  min-height: calc(100vh - 200px);
  overflow-y: auto;
}

.profile-section {
  background-color: #1F2937;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #374151;
}

.profile-section h3 {
  color: #F3F4F6;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #374151;
  padding-bottom: 0.5rem;
}

.profile-info-item {
  display: flex;
  margin-bottom: 0.75rem;
  align-items: center;
}

.profile-info-label {
  width: 40%;
  color: #9CA3AF;
  font-size: 0.875rem;
}

.profile-info-value {
  width: 60%;
  color: #F3F4F6;
  font-weight: 500;
}

.profile-toggle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #374151;
}

.profile-toggle-label {
  color: #F3F4F6;
  font-size: 0.875rem;
}

/* Modern Toggle Switch Styling */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-surface-light);
  transition: all var(--transition-fast);
  border-radius: 24px;
  border: 2px solid var(--color-border);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-slider:hover {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.1);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
  transition: all var(--transition-fast);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.2);
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

input:focus + .toggle-slider {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-custom-rgb), 0.2);
}

input:disabled + .toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Koyu tema için stil tanımlamaları */
.dark-mode {
  --color-bg-primary: #111827;
  --color-bg-secondary: #1F2937;
  --color-text-primary: #F9FAFB;
  --color-text-secondary: #D1D5DB;
}

#taskCreatorForm input,
#taskCreatorForm select,
#taskCreatorForm textarea {
  width: 100%;
  background-color: #374151;
  border: 1px solid #4B5563;
  border-radius: 0.375rem;
  padding: 0.5rem;
  color: #F3F4F6;
}

#taskCreatorForm input:focus,
#taskCreatorForm select:focus,
#taskCreatorForm textarea:focus {
  outline: none;
  border-color: #60A5FA;
}

#taskCreatorForm label {
  color: #E5E7EB;
  margin-bottom: 0.25rem;
  display: block;
}

/* Form butonları için stil */
#taskCreatorForm button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

#taskCreatorForm button[type="button"] {
  background-color: #4B5563;
}

#taskCreatorForm button[type="button"]:hover {
  background-color: #374151;
}

#taskCreatorForm button[onclick="createTask()"] {
  background-color: #3B82F6;
}

#taskCreatorForm button[onclick="createTask()"]:hover {
  background-color: #2563EB;
}

/* Modal stilleri */
#taskModal {
    backdrop-filter: blur(4px);
}

#taskModal input {
    transition: border-color 0.2s ease;
}

#taskModal input:focus {
    border-color: #60A5FA;
    outline: none;
}

#taskModal button {
    transition: all 0.2s ease;
}

#taskModal button:hover {
    transform: translateY(-1px);
}

.truncate-cell {
    max-width: 100px; /* Masaüstü görünümde maksimum genişlik */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
}

/* Mobil responsive için medya sorgusu */
@media (max-width: 768px) {
    .truncate-cell {
        max-width: 80px; /* Daha küçük ekranlarda daha az genişlik */
    }
}

/* Seçim sırasında text seçimini engelle */
.payment-row, #dataViewerTableBody tr, #paymentsTable tbody tr {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Eski payment-row stilleri - geriye dönük uyumluluk için */
.payment-row.selected {
    background: rgba(var(--color-custom-rgb), 0.15) !important;
    border-left: 3px solid var(--color-primary);
}

.payment-row:hover:not(.selected) {
    background: var(--color-surface-hover);
}

/* Context Menu Stilleri */
#paymentsContextMenu {
    min-width: 200px;
}

/* Devre dışı bırakılmış elementler için stil */
.cursor-not-allowed {
    cursor: not-allowed;
}

#paymentsContextMenu ul li {
    position: relative;
}

#paymentsContextMenu ul li:hover > ul {
    display: block;
}

#paymentsContextMenu ul li ul {
    position: absolute;
    left: 100%;
    top: 0;
    min-width: 150px;
}

/* Alt menü ok işareti için stil */
.fa-chevron-right {
    font-size: 0.8em;
}

/* Tüm context menüler için white-space nowrap */
#qaTaskContextMenu,
#qaTaskContextMenu button,
#qaTaskContextMenu span,
#qaTaskContextMenu li,
#dataViewerContextMenu,
#dataViewerContextMenu li,
#gamesDataContextMenu,
#gamesDataContextMenu li,
#videoSsContextMenu,
#videoSsContextMenu li,
#fileHashContextMenu,
#fileHashContextMenu li,
#paymentsContextMenu,
#paymentsContextMenu li {
    white-space: nowrap !important;
}

/* Genel context menu öğeleri için */
.context-menu,
.context-menu li,
.context-menu button,
.context-menu span,
[id*="ContextMenu"],
[id*="ContextMenu"] li,
[id*="ContextMenu"] button,
[id*="ContextMenu"] span {
    white-space: nowrap !important;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.animate-slideIn {
    animation: slideIn 0.3s ease-out;
}

.animate-fadeOut {
    animation: fadeOut 0.3s ease-out;
}

.notification {
    position: relative;
    transition: transform 0.3s ease-out;
}

/* TestRail responsive düzenlemeleri */
@media (max-width: 1200px) {
  #testrail .flex, #testrail .w-1\/3, #testrail .flex-1 {
    flex-direction: column !important;
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }
  #testrail .bg-gray-700, #testrail .bg-gray-600, #testrail .bg-gray-800 {
    min-width: 0 !important;
    max-width: 100% !important;
    overflow-x: auto !important;
  }
  #testrail .rounded, #testrail .p-4 {
    border-radius: 0 !important;
    padding: 0.5rem !important;
  }
  #testrail table {
    font-size: 0.95em;
  }
  #testrail .bg-gray-700,
  #testrail .bg-gray-600,
  #testrail .bg-gray-800 {
    max-height: none !important;
    height: auto !important;
    overflow-y: visible !important;
  }
  #testrail .scrollable-content,
  #testrail .p-4,
  #testrail .rounded {
    max-height: none !important;
    height: auto !important;
    overflow-y: visible !important;
  }
}

@media (max-width: 900px) {
  #testrail .flex, #testrail .w-1\/3, #testrail .flex-1 {
    flex-direction: column !important;
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }
  #testrail .bg-gray-700, #testrail .bg-gray-600, #testrail .bg-gray-800 {
    min-width: 0 !important;
    max-width: 100% !important;
    overflow-x: auto !important;
  }
  #testrail table {
    display: block;
    width: 100%;
    overflow-x: auto;
    font-size: 0.9em;
  }
  #testrail .bg-gray-700,
  #testrail .bg-gray-600,
  #testrail .bg-gray-800 {
    max-height: none !important;
    height: auto !important;
    overflow-y: visible !important;
  }
  #testrail .scrollable-content,
  #testrail .p-4,
  #testrail .rounded {
    max-height: none !important;
    height: auto !important;
    overflow-y: visible !important;
  }
}

#testrail table {
  overflow-x: auto;
  width: 100%;
  min-width: 600px;
}

#testrail .bg-gray-700, #testrail .bg-gray-600, #testrail .bg-gray-800 {
  overflow-x: auto;
}

